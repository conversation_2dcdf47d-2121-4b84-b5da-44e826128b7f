#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import numpy as np
import matplotlib.pyplot as plt

def load_evaluation_results(file_path='models/angel_investor_ner_evaluation.json'):
    """
    加载评估结果

    Args:
        file_path: 评估结果文件路径

    Returns:
        评估结果
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_entity_types(results):
    """
    分析不同实体类型的性能

    Args:
        results: 评估结果

    Returns:
        实体类型性能分析
    """
    report = results['report']

    # 解析分类报告
    lines = report.strip().split('\n')
    entity_metrics = {}

    for line in lines[2:-3]:  # 跳过标题行和汇总行
        parts = line.strip().split()
        if len(parts) >= 5:
            entity_type = parts[0]
            precision = float(parts[1])
            recall = float(parts[2])
            f1 = float(parts[3])
            support = int(parts[4])

            entity_metrics[entity_type] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'support': support
            }

    return entity_metrics

def plot_entity_performance(entity_metrics):
    """
    绘制不同实体类型的性能

    Args:
        entity_metrics: 实体类型性能分析
    """
    entity_types = list(entity_metrics.keys())
    precision = [entity_metrics[et]['precision'] for et in entity_types]
    recall = [entity_metrics[et]['recall'] for et in entity_types]
    f1 = [entity_metrics[et]['f1'] for et in entity_types]

    x = np.arange(len(entity_types))
    width = 0.25

    fig, ax = plt.subplots(figsize=(10, 6))
    rects1 = ax.bar(x - width, precision, width, label='Precision')
    rects2 = ax.bar(x, recall, width, label='Recall')
    rects3 = ax.bar(x + width, f1, width, label='F1')

    ax.set_xlabel('Entity Type')
    ax.set_ylabel('Score')
    ax.set_title('Performance by Entity Type')
    ax.set_xticks(x)
    ax.set_xticklabels(entity_types)
    ax.legend()

    plt.tight_layout()
    plt.savefig('models/entity_performance.png')
    plt.close()

def analyze_model_usability(results, entity_metrics):
    """
    分析模型可用性

    Args:
        results: 评估结果
        entity_metrics: 实体类型性能分析

    Returns:
        可用性分析报告
    """
    # 整体性能分析
    overall_precision = results['precision']
    overall_recall = results['recall']
    overall_f1 = results['f1']

    # 实体类型性能分析
    best_entity = max(entity_metrics.items(), key=lambda x: x[1]['f1'])
    worst_entity = min(entity_metrics.items(), key=lambda x: x[1]['f1'])

    # 可用性分析报告
    report = {
        'overall_performance': {
            'precision': overall_precision,
            'recall': overall_recall,
            'f1': overall_f1
        },
        'entity_performance': {
            'best': {
                'type': best_entity[0],
                'metrics': best_entity[1]
            },
            'worst': {
                'type': worst_entity[0],
                'metrics': worst_entity[1]
            }
        },
        'usability_analysis': {
            'strengths': [],
            'weaknesses': [],
            'overall_assessment': ''
        },
        'optimization_suggestions': []
    }

    # 分析优势
    if overall_f1 > 0.7:
        report['usability_analysis']['strengths'].append("模型整体F1分数较高，具有良好的识别能力")

    for entity_type, metrics in entity_metrics.items():
        if metrics['f1'] > 0.8:
            report['usability_analysis']['strengths'].append(f"{entity_type}实体的识别效果很好，F1分数为{metrics['f1']:.2f}")
        elif metrics['precision'] > 0.8:
            report['usability_analysis']['strengths'].append(f"{entity_type}实体的精确率高，可以减少误报")

    # 分析劣势
    if overall_f1 < 0.6:
        report['usability_analysis']['weaknesses'].append("模型整体F1分数较低，识别能力有限")

    for entity_type, metrics in entity_metrics.items():
        if metrics['f1'] < 0.6:
            report['usability_analysis']['weaknesses'].append(f"{entity_type}实体的识别效果较差，F1分数仅为{metrics['f1']:.2f}")
        elif metrics['recall'] < 0.6:
            report['usability_analysis']['weaknesses'].append(f"{entity_type}实体的召回率低，容易漏掉实体")

    # 整体评估
    if overall_f1 > 0.7:
        report['usability_analysis']['overall_assessment'] = "模型整体表现良好，可以用于天使投资公司网页文本的实体识别任务"
    elif overall_f1 > 0.5:
        report['usability_analysis']['overall_assessment'] = "模型表现一般，可以用于辅助识别，但需要人工校验"
    else:
        report['usability_analysis']['overall_assessment'] = "模型表现较差，不建议直接用于实际应用，需要进一步优化"

    # 优化建议
    report['optimization_suggestions'] = [
        "增加训练数据量，特别是对于识别效果较差的实体类型",
        "使用预训练的词嵌入（如Word2Vec、GloVe或BERT）提高模型性能",
        "调整模型超参数，如LSTM单元数量、嵌入维度等",
        "尝试使用更复杂的模型架构，如BERT-BiLSTM-CRF",
        "引入领域特定的特征，如投资领域的专业词汇表",
        "使用数据增强技术，如同义词替换、回译等",
        "对于识别效果较差的实体类型，可以考虑使用规则辅助识别"
    ]

    return report

def main():
    # 加载评估结果
    print("加载评估结果...")
    try:
        results = load_evaluation_results()
    except FileNotFoundError:
        print("评估结果文件不存在，请先运行 angel_investor_ner.py")
        return

    # 分析不同实体类型的性能
    print("分析不同实体类型的性能...")
    entity_metrics = analyze_entity_types(results)

    # 绘制不同实体类型的性能
    print("绘制不同实体类型的性能...")
    plot_entity_performance(entity_metrics)

    # 分析模型可用性
    print("分析模型可用性...")
    usability_report = analyze_model_usability(results, entity_metrics)

    # 保存可用性分析报告
    with open('models/model_usability_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(usability_report, f, ensure_ascii=False, indent=2)

    # 打印可用性分析报告
    print("\n模型可用性分析报告:")
    print(f"整体性能: 精确率={usability_report['overall_performance']['precision']:.4f}, 召回率={usability_report['overall_performance']['recall']:.4f}, F1分数={usability_report['overall_performance']['f1']:.4f}")

    print("\n实体类型性能:")
    best_type = usability_report['entity_performance']['best']['type']
    best_f1 = usability_report['entity_performance']['best']['metrics']['f1']
    worst_type = usability_report['entity_performance']['worst']['type']
    worst_f1 = usability_report['entity_performance']['worst']['metrics']['f1']
    print(f"最佳: {best_type} (F1={best_f1:.4f})")
    print(f"最差: {worst_type} (F1={worst_f1:.4f})")

    print("\n优势:")
    for strength in usability_report['usability_analysis']['strengths']:
        print(f"- {strength}")

    print("\n劣势:")
    for weakness in usability_report['usability_analysis']['weaknesses']:
        print(f"- {weakness}")

    print("\n整体评估:")
    print(usability_report['usability_analysis']['overall_assessment'])

    print("\n优化建议:")
    for i, suggestion in enumerate(usability_report['optimization_suggestions']):
        print(f"{i+1}. {suggestion}")

    print("\n可用性分析报告已保存到 models/model_usability_analysis.json")
    print("实体性能图表已保存到 models/entity_performance.png")

if __name__ == "__main__":
    main()
