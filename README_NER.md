# 基于神经网络的实体识别系统

本项目构建了一个基于BiLSTM-CRF的实体识别系统，用于从天使投资公司网页文本中识别实体，如组织机构、人物、地点和时间等。

## 项目结构

- `bilstm_crf_ner.py`: BiLSTM-CRF模型的实现
- `train_ner_model.py`: 训练和评估神经网络模型
- `angel_investor_ner.py`: 标注天使投资公司网页文本并进行实体识别
- `model_analysis.py`: 分析模型可用性并提出优化方案
- `run_ner_system.py`: 主程序，运行整个流程

## 依赖库

运行本项目需要安装以下Python库：

```bash
pip install tensorflow tensorflow-addons numpy matplotlib scikit-learn seqeval
```

## 使用方法

1. 运行主程序：

```bash
python run_ner_system.py
```

或者分步运行：

```bash
# 步骤1：训练和评估神经网络模型
python train_ner_model.py

# 步骤2：标注天使投资公司网页文本并进行实体识别
python angel_investor_ner.py

# 步骤3：分析模型可用性并提出优化方案
python model_analysis.py
```

## 输出文件

- `ner_model.h5`: 训练好的BiLSTM-CRF模型
- `ner_vocab.json`: 模型词汇表
- `training_history.png`: 训练历史图表
- `angel_investor_ner_evaluation.json`: 实体识别评估结果
- `model_usability_analysis.json`: 模型可用性分析报告
- `entity_performance.png`: 实体类型性能图表

## BiLSTM-CRF模型原理

BiLSTM-CRF（双向长短期记忆网络-条件随机场）是一种用于序列标注任务的神经网络模型，特别适用于命名实体识别（NER）任务。它由两个主要部分组成：

### 1. 双向LSTM（BiLSTM）

- **LSTM（长短期记忆网络）**：是一种特殊的循环神经网络（RNN），能够学习长距离依赖关系，解决了传统RNN的梯度消失问题。
- **双向（Bidirectional）**：同时从前向和后向两个方向处理序列，捕获更全面的上下文信息。
- **作用**：BiLSTM层能够为序列中的每个词生成一个特征向量，这个向量包含了该词的上下文信息。

### 2. 条件随机场（CRF）

- **CRF**：是一种概率图模型，用于对序列数据进行标注。
- **作用**：CRF层考虑了标签之间的依赖关系，确保预测的标签序列是合法的（例如，I-ORG标签不会出现在O标签之后，而应该跟在B-ORG之后）。

### 工作流程

1. **输入层**：接收词语的索引序列。
2. **嵌入层**：将词语索引转换为密集向量表示。
3. **BiLSTM层**：从前向和后向两个方向处理序列，生成上下文感知的特征表示。
4. **全连接层**：将BiLSTM的输出映射到标签空间。
5. **CRF层**：考虑标签之间的依赖关系，输出最优的标签序列。

### 优势

- 能够捕获长距离依赖关系
- 考虑了标签之间的依赖关系
- 适用于各种序列标注任务
- 在命名实体识别任务中表现优异

## 实体类型

本项目识别以下类型的实体：

- **ORG**：组织机构，如"红杉资本"、"IDG资本"等
- **PER**：人物，如"张磊"、"徐小平"等
- **LOC**：地点，如"北京"、"上海"等
- **TIME**：时间，如"2005年"、"1992年"等

## 评估指标

- **精确率（Precision）**：正确识别的实体数量 / 识别出的实体总数
- **召回率（Recall）**：正确识别的实体数量 / 实际实体总数
- **F1分数**：精确率和召回率的调和平均值，综合评估模型性能

## 优化方案

1. **增加训练数据量**：特别是对于识别效果较差的实体类型
2. **使用预训练的词嵌入**：如Word2Vec、GloVe或BERT
3. **调整模型超参数**：如LSTM单元数量、嵌入维度等
4. **尝试更复杂的模型架构**：如BERT-BiLSTM-CRF
5. **引入领域特定的特征**：如投资领域的专业词汇表
6. **使用数据增强技术**：如同义词替换、回译等
7. **规则辅助识别**：对于识别效果较差的实体类型，可以考虑使用规则辅助识别
