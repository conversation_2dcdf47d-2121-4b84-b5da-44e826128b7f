digraph AngelInvestmentOntology {
    // 图形设置
    rankdir=TB;
    node [shape=box, style=filled, fillcolor=lightblue, fontname="SimSun"];
    edge [fontname="SimSun"];
    
    // 核心概念节点
    Thing [label="Thing\n(事物)"];
    
    // 一级概念
    InvestmentInstitution [label="InvestmentInstitution\n(投资机构)"];
    Person [label="Person\n(人物)"];
    Company [label="Company\n(公司)"];
    InvestmentEvent [label="InvestmentEvent\n(投资事件)"];
    InvestmentRound [label="InvestmentRound\n(投资轮次)"];
    InvestmentField [label="InvestmentField\n(投资领域)"];
    Location [label="Location\n(地理位置)"];
    
    // 二级概念
    AngelInvestor [label="AngelInvestor\n(天使投资机构)"];
    VentureCapital [label="VentureCapital\n(风险投资机构)"];
    
    Investor [label="Investor\n(投资人)"];
    Founder [label="Founder\n(创始人)"];
    
    StartupCompany [label="StartupCompany\n(创业公司)"];
    
    AngelRound [label="AngelRound\n(天使轮)"];
    RoundA [label="RoundA\n(A轮)"];
    RoundB [label="RoundB\n(B轮)"];
    
    Technology [label="Technology\n(科技)"];
    Healthcare [label="Healthcare\n(医疗健康)"];
    Education [label="Education\n(教育)"];
    
    City [label="City\n(城市)"];
    Province [label="Province\n(省份)"];
    Country [label="Country\n(国家)"];
    
    // 概念层次关系
    Thing -> InvestmentInstitution;
    Thing -> Person;
    Thing -> Company;
    Thing -> InvestmentEvent;
    Thing -> InvestmentRound;
    Thing -> InvestmentField;
    Thing -> Location;
    
    InvestmentInstitution -> AngelInvestor;
    InvestmentInstitution -> VentureCapital;
    
    Person -> Investor;
    Person -> Founder;
    
    Company -> StartupCompany;
    
    InvestmentRound -> AngelRound;
    InvestmentRound -> RoundA;
    InvestmentRound -> RoundB;
    
    InvestmentField -> Technology;
    InvestmentField -> Healthcare;
    InvestmentField -> Education;
    
    Location -> City;
    Location -> Province;
    Location -> Country;
    
    // 关系定义
    edge [color=red, fontcolor=red];
    
    InvestmentInstitution -> Company [label="invests\n(投资)"];
    Investor -> Company [label="invests\n(投资)"];
    
    Founder -> InvestmentInstitution [label="founded\n(创立)"];
    Founder -> Company [label="founded\n(创立)"];
    
    InvestmentInstitution -> Founder [label="hasFounder\n(有创始人)"];
    Company -> Founder [label="hasFounder\n(有创始人)"];
    
    Investor -> InvestmentInstitution [label="manages\n(管理)"];
    
    InvestmentInstitution -> InvestmentEvent [label="hasInvestmentEvent\n(有投资事件)"];
    Company -> InvestmentEvent [label="hasInvestmentEvent\n(有投资事件)"];
    
    InvestmentEvent -> InvestmentRound [label="hasRound\n(有轮次)"];
    
    InvestmentInstitution -> InvestmentField [label="focusesOn\n(关注领域)"];
    Company -> InvestmentField [label="belongsTo\n(属于领域)"];
    
    InvestmentInstitution -> Location [label="locatedIn\n(位于)"];
    Company -> Location [label="locatedIn\n(位于)"];
    
    InvestmentInstitution -> Location [label="investsIn\n(投资区域)"];
    
    // 属性定义
    edge [color=blue, fontcolor=blue, style=dashed];
    
    Thing -> Thing [label="name\n(名称)"];
    Thing -> Thing [label="description\n(描述)"];
    
    InvestmentInstitution -> InvestmentInstitution [label="foundingDate\n(成立日期)"];
    InvestmentInstitution -> InvestmentInstitution [label="assetsUnderManagement\n(管理资产规模)"];
    
    Investor -> Investor [label="position\n(职位)"];
    Investor -> Investor [label="background\n(背景)"];
    
    Company -> Company [label="foundingDate\n(成立日期)"];
    Company -> Company [label="valuation\n(估值)"];
    
    InvestmentEvent -> InvestmentEvent [label="investmentAmount\n(投资金额)"];
    InvestmentEvent -> InvestmentEvent [label="investmentDate\n(投资日期)"];
}
