#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
from collections import defaultdict

def load_enriched_data(file_path='angel_investors_with_baike.json'):
    """
    加载带有百度百科信息的天使投资公司数据
    
    Args:
        file_path: 数据文件路径
        
    Returns:
        带有百度百科信息的天使投资公司列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在，请先运行 baidu_baike_crawler.py 脚本")
        return []
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return []

def build_knowledge_graph(investors):
    """
    构建知识图谱
    
    Args:
        investors: 带有百度百科信息的投资公司列表
        
    Returns:
        知识图谱数据
    """
    # 初始化知识图谱
    knowledge_graph = {
        'nodes': [],
        'edges': []
    }
    
    # 节点ID映射，避免重复
    node_ids = {}
    
    # 添加投资公司节点
    for investor in investors:
        investor_id = f"investor_{investor['id']}"
        investor_name = investor['name']
        
        # 添加投资公司节点
        investor_node = {
            'id': investor_id,
            'label': investor_name,
            'type': 'investor'
        }
        
        # 添加百度百科信息
        if 'baidu_baike' in investor and 'info' in investor['baidu_baike']:
            for key, value in investor['baidu_baike']['info'].items():
                if key != '摘要':  # 摘要通常太长
                    investor_node[key] = value
        
        knowledge_graph['nodes'].append(investor_node)
        node_ids[investor_id] = True
        
        # 处理投资的公司
        if 'invested_companies' in investor:
            for company in investor['invested_companies']:
                company_id = f"company_{company['id']}"
                
                # 如果公司节点不存在，添加公司节点
                if company_id not in node_ids:
                    company_node = {
                        'id': company_id,
                        'label': f"公司_{company['id']}",
                        'type': 'company'
                    }
                    knowledge_graph['nodes'].append(company_node)
                    node_ids[company_id] = True
                
                # 添加投资关系边
                edge = {
                    'source': investor_id,
                    'target': company_id,
                    'label': '投资',
                    'round': company.get('round', ''),
                    'date': company.get('date', '')
                }
                knowledge_graph['edges'].append(edge)
        
        # 处理投资领域
        if 'baidu_baike' in investor and 'info' in investor['baidu_baike']:
            info = investor['baidu_baike']['info']
            
            # 尝试从不同字段获取投资领域信息
            fields_str = info.get('投资领域', '') or info.get('业务领域', '') or info.get('经营范围', '')
            
            if fields_str:
                # 分割领域
                fields = [field.strip() for field in re.split(r'[,，;；、]', fields_str) if field.strip()]
                
                for field in fields:
                    field_id = f"field_{field}"
                    
                    # 如果领域节点不存在，添加领域节点
                    if field_id not in node_ids:
                        field_node = {
                            'id': field_id,
                            'label': field,
                            'type': 'field'
                        }
                        knowledge_graph['nodes'].append(field_node)
                        node_ids[field_id] = True
                    
                    # 添加投资领域关系边
                    edge = {
                        'source': investor_id,
                        'target': field_id,
                        'label': '关注领域'
                    }
                    knowledge_graph['edges'].append(edge)
            
            # 处理创始人
            founder = info.get('创始人', '')
            if founder:
                founders = [f.strip() for f in re.split(r'[,，;；、]', founder) if f.strip()]
                
                for founder_name in founders:
                    founder_id = f"person_{founder_name}"
                    
                    # 如果创始人节点不存在，添加创始人节点
                    if founder_id not in node_ids:
                        founder_node = {
                            'id': founder_id,
                            'label': founder_name,
                            'type': 'person'
                        }
                        knowledge_graph['nodes'].append(founder_node)
                        node_ids[founder_id] = True
                    
                    # 添加创始人关系边
                    edge = {
                        'source': founder_id,
                        'target': investor_id,
                        'label': '创立'
                    }
                    knowledge_graph['edges'].append(edge)
    
    return knowledge_graph

def analyze_knowledge_graph(kg):
    """
    分析知识图谱
    
    Args:
        kg: 知识图谱数据
    """
    # 统计节点类型
    node_types = defaultdict(int)
    for node in kg['nodes']:
        node_types[node.get('type', 'unknown')] += 1
    
    print("\n知识图谱节点类型分布:")
    for node_type, count in node_types.items():
        print(f"  {node_type}: {count}个节点")
    
    # 统计边类型
    edge_types = defaultdict(int)
    for edge in kg['edges']:
        edge_types[edge.get('label', 'unknown')] += 1
    
    print("\n知识图谱边类型分布:")
    for edge_type, count in edge_types.items():
        print(f"  {edge_type}: {count}条边")
    
    # 统计投资轮次
    round_types = defaultdict(int)
    for edge in kg['edges']:
        if edge.get('label') == '投资':
            round_types[edge.get('round', '未知')] += 1
    
    print("\n投资轮次分布:")
    for round_type, count in sorted(round_types.items(), key=lambda x: x[1], reverse=True):
        print(f"  {round_type}: {count}次投资")

def export_graph_data(kg):
    """
    导出知识图谱数据为JSON格式
    
    Args:
        kg: 知识图谱数据
    """
    output_file = 'angel_investment_knowledge_graph.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(kg, f, ensure_ascii=False, indent=2)
    
    print(f"\n已将知识图谱数据保存到: {output_file}")

def main():
    # 加载带有百度百科信息的天使投资公司数据
    investors = load_enriched_data()
    
    if not investors:
        return
    
    print(f"已加载 {len(investors)} 家投资公司数据")
    
    # 构建知识图谱
    knowledge_graph = build_knowledge_graph(investors)
    
    print(f"\n知识图谱构建完成，包含 {len(knowledge_graph['nodes'])} 个节点和 {len(knowledge_graph['edges'])} 条边")
    
    # 分析知识图谱
    analyze_knowledge_graph(knowledge_graph)
    
    # 导出知识图谱数据
    export_graph_data(knowledge_graph)

if __name__ == "__main__":
    main()
