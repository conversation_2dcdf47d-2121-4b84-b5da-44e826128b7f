#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
import re
import random
import logging
from urllib.parse import quote
import requests
from bs4 import BeautifulSoup
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import aiohttp
import asyncio
from fake_useragent import UserAgent

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='encyclopedia_crawler.log'
)
logger = logging.getLogger(__name__)

# 反爬虫配置
USE_PROXY = True  # 是否使用代理
PROXY_API = "http://your-proxy-api.com/get_proxy"  # 代理IP获取API
MAX_RETRIES = 2  # 最大重试次数
MIN_DELAY = 1  # 最小延迟(秒)
MAX_DELAY = 3  # 最大延迟(秒)
BATCH_DELAY = 10  # 批次间延迟(秒)

# 并发配置
MAX_WORKERS = 5  # 最大线程数
BATCH_SIZE = 10  # 每批处理的公司数量

# 百科URL模板
ENCYCLOPEDIA_URLS = {
    'baidu': "https://baike.baidu.com/item/{}",
    'wikipedia': "https://zh.wikipedia.org/wiki/{}",
    'so': "https://baike.so.com/doc/{}",
    'military': "https://military.baidu.com/item/{}",
    'finance': "https://finance.baidu.com/baike/{}"
}

# 搜索引擎URL
SEARCH_ENGINES = {
    'baidu': "https://www.baidu.com/s?wd={}",
    'bing': "https://cn.bing.com/search?q={}"
}

# 代理池
proxy_pool = []

def load_angel_investors(file_path='models/angel_investors.json'):
    """加载投资公司数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"文件 {file_path} 不存在")
        return []
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return []

def get_proxy():
    """获取代理IP"""
    global proxy_pool
    
    if not proxy_pool:
        try:
            # 从代理API获取一批代理
            response = requests.get(PROXY_API, timeout=10)
            if response.status_code == 200:
                proxy_pool = response.json().get('proxies', [])
                random.shuffle(proxy_pool)
                logger.info(f"获取到 {len(proxy_pool)} 个代理IP")
        except Exception as e:
            logger.error(f"获取代理IP失败: {e}")
    
    if proxy_pool:
        return proxy_pool.pop()
    return None

def create_session():
    """创建带重试机制和连接池的请求会话"""
    session = requests.Session()
    retry = Retry(
        total=MAX_RETRIES,
        backoff_factor=1,
        status_forcelist=[500, 502, 503, 504, 429]  # 增加429状态码(请求过多)
    )
    adapter = HTTPAdapter(max_retries=retry, pool_connections=100, pool_maxsize=100)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session

async def fetch_page_async(url, session, retries=MAX_RETRIES):
    """异步页面获取函数"""
    ua = UserAgent()
    
    for attempt in range(retries):
        try:
            # 随机更换User-Agent
            headers = {
                'User-Agent': ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Connection': 'keep-alive',
            }
            
            # 随机选择代理
            proxy = get_proxy() if USE_PROXY else None
            
            async with session.get(url, headers=headers, proxy=proxy, timeout=30) as response:
                if response.status == 200:
                    content = await response.text()
                    # 检查是否被反爬
                    if "验证码" in content or "安全验证" in content:
                        raise Exception("触发反爬机制")
                    return content
                else:
                    logger.warning(f"请求返回状态码: {response.status}")
                    if response.status == 429:  # 请求过多
                        await asyncio.sleep(random.uniform(5, 10))  # 长时间等待
        except Exception as e:
            logger.warning(f"尝试 {attempt+1}/{retries} 获取页面 {url} 时出错: {e}")
            if attempt < retries - 1:
                wait_time = random.uniform(MIN_DELAY, MAX_DELAY)
                logger.info(f"等待 {wait_time:.2f} 秒后重试")
                await asyncio.sleep(wait_time)
    return None

async def search_encyclopedia_async(keyword, encyclopedia='baidu', session=None):
    """异步在指定百科中搜索关键词"""
    encoded_keyword = quote(keyword)
    search_url = SEARCH_ENGINES['baidu'].format(f"{keyword} {encyclopedia}百科")
    
    logger.info(f"在{encyclopedia}百科中搜索: {keyword}")
    
    # 创建新会话或使用传入的会话
    if not session:
        timeout = aiohttp.ClientTimeout(total=60)
        session = aiohttp.ClientSession(timeout=timeout)
    
    html_content = await fetch_page_async(search_url, session)
    
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 根据不同百科调整搜索结果解析逻辑
    if encyclopedia == 'baidu':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "baike.baidu.com/item" in href:
                try:
                    # 使用HEAD请求获取最终URL
                    async with session.head(href, timeout=10) as response:
                        return str(response.url)
                except:
                    continue
    elif encyclopedia == 'wikipedia':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "zh.wikipedia.org/wiki" in href:
                return href
    elif encyclopedia == 'so':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "baike.so.com/doc" in href:
                return href
    
    logger.info(f"未在{encyclopedia}百科中找到 {keyword} 的链接")
    return None

def parse_baidu_baike(html_content):
    """解析百度百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 尝试新老两种知识盒子结构
    basic_info = soup.find('div', class_='basic-info J-basic-info')
    if not basic_info:
        basic_info = soup.find('div', class_='basic-info')
    
    if basic_info:
        dt_elements = basic_info.find_all('dt', class_='basicInfo-item name')
        dd_elements = basic_info.find_all('dd', class_='basicInfo-item value')
        
        for dt, dd in zip(dt_elements, dd_elements):
            key = dt.get_text(strip=True).replace('\xa0', '')
            value = dd.get_text(strip=True).replace('\xa0', ' ')
            result[key] = value
    
    # 提取摘要
    summary = soup.find('div', class_='lemma-summary') or soup.find('div', class_='J-summary')
    if summary:
        result['摘要'] = summary.get_text(strip=True)
    
    return result

def parse_wikipedia(html_content):
    """解析维基百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 解析信息框
    infobox = soup.find('table', class_='infobox')
    if infobox:
        rows = infobox.find_all('tr')
        for row in rows:
            th = row.find('th')
            td = row.find('td')
            if th and td:
                key = th.get_text(strip=True)
                value = td.get_text(strip=True)
                result[key] = value
    
    # 提取摘要
    summary = soup.find('div', class_='mw-parser-output')
    if summary and summary.p:
        result['摘要'] = summary.p.get_text(strip=True)
    
    return result

def parse_so_baike(html_content):
    """解析360百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 解析信息栏
    info_box = soup.find('div', class_='basic-info')
    if info_box:
        items = info_box.find_all('div', class_='item')
        for item in items:
            name = item.find('div', class_='name')
            value = item.find('div', class_='value')
            if name and value:
                key = name.get_text(strip=True)
                value_text = value.get_text(strip=True)
                result[key] = value_text
    
    # 提取摘要
    summary = soup.find('div', class_='summary')
    if summary:
        result['摘要'] = summary.get_text(strip=True)
    
    return result

def extract_company_info(knowledge_box, source):
    """从知识盒子中提取公司信息，适配不同百科"""
    # 字段映射字典，处理不同百科的差异
    field_mappings = {
        'baidu': {
            '成立时间': ['成立时间'],
            '公司类型': ['公司类型'],
            '总部地点': ['总部地点'],
            '创始人': ['创始人'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本'],
            '经营范围': ['经营范围'],
            '业务领域': ['业务领域', '业务范围'],
            '投资领域': ['投资领域']
        },
        'wikipedia': {
            '成立时间': ['成立时间', '成立'],
            '公司类型': ['公司类型', '企业类型'],
            '总部地点': ['总部地点', '总部所在地'],
            '创始人': ['创始人', '创立者'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本', '实收资本'],
            '经营范围': ['经营范围', '业务范围'],
            '业务领域': ['业务领域', '产业'],
            '投资领域': ['投资领域']
        },
        'so': {
            '成立时间': ['成立时间'],
            '公司类型': ['公司类型'],
            '总部地点': ['总部地点'],
            '创始人': ['创始人'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本'],
            '经营范围': ['经营范围'],
            '业务领域': ['业务领域', '业务范围'],
            '投资领域': ['投资领域']
        }
    }
    
    company_info = {}
    mapping = field_mappings.get(source, field_mappings['baidu'])
    
    for target, sources in mapping.items():
        for source_key in sources:
            if source_key in knowledge_box:
                company_info[target] = knowledge_box[source_key]
                break
    
    # 提取摘要
    if '摘要' in knowledge_box:
        company_info['摘要'] = knowledge_box['摘要']
    
    return {k: v for k, v in company_info.items() if v}

async def crawl_company_info_async(company_name):
    """
    异步尝试从多个百科获取公司信息
    优先顺序: 百度百科 -> 维基百科 -> 360百科
    """
    logger.info(f"开始爬取 {company_name} 的信息")
    result = {
        'company_name': company_name,
        'sources': [],
        'info': {},
        'status': 'fail'
    }
    
    timeout = aiohttp.ClientTimeout(total=120)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        # 尝试的百科列表及对应解析函数
        encyclopedias = [
            ('baidu', parse_baidu_baike),
            ('wikipedia', parse_wikipedia),
            ('so', parse_so_baike)
        ]
        
        for source, parser in encyclopedias:
            logger.info(f"尝试从 {source} 百科获取 {company_name} 的信息")
            
            # 直接构建URL
            encoded_name = quote(company_name)
            url = ENCYCLOPEDIA_URLS[source].format(encoded_name)
            
            # 获取页面内容
            html_content = await fetch_page_async(url, session)
            
            # 如果直接访问失败，尝试搜索
            if not html_content or "未找到" in html_content or "不存在" in html_content:
                logger.info(f"直接访问失败，尝试在 {source} 百科中搜索 {company_name}")
                url = await search_encyclopedia_async(company_name, source, session)
                if url:
                    html_content = await fetch_page_async(url, session)
            
            # 解析内容
            if html_content:
                knowledge_box = parser(html_content)
                
                if knowledge_box:
                    company_info = extract_company_info(knowledge_box, source)
                    
                    if company_info:
                        result['sources'].append({
                            'source': source,
                            'url': url,
                            'raw_data': knowledge_box
                        })
                        
                        # 合并信息，后面的百科可能补充更多信息
                        result['info'].update(company_info)
                        result['status'] = 'success'
                        logger.info(f"从 {source} 百科成功获取到 {company_name} 的信息")
                        
                        # 如果获取到关键信息，不再尝试其他百科
                        if len(company_info) >= 5:
                            break
            
            # 随机延迟，避免请求过于频繁
            wait_time = random.uniform(MIN_DELAY, MAX_DELAY)
            await asyncio.sleep(wait_time)
    
    return result

async def process_company_batch(companies, batch_num):
    """异步处理一批公司"""
    print(f"\n开始处理第 {batch_num} 批公司，共 {len(companies)} 家")
    
    tasks = []
    for company in companies:
        task = asyncio.create_task(crawl_company_info_async(company['name']))
        tasks.append(task)
    
    # 添加随机批次内延迟，减少集中请求
    await asyncio.sleep(random.uniform(1, 2))
    
    results = await asyncio.gather(*tasks)
    
    # 关联结果与公司
    for i, result in enumerate(results):
        companies[i]['encyclopedia'] = result
        if result['status'] == 'success':
            print(f"成功从 {', '.join([s['source'] for s in result['sources']])} 百科获取 {companies[i]['name']} 的信息")
        else:
            print(f"未能从任何百科获取 {companies[i]['name']} 的有效信息")
    
    # 保存单个公司的数据
    os.makedirs('data/multi_encyclopedia_data', exist_ok=True)
    for company in companies:
        company_file = os.path.join('data/multi_encyclopedia_data', f"{company['id']}.json")
        with open(company_file, 'w', encoding='utf-8') as f:
            json.dump(company, f, ensure_ascii=False, indent=2)
    
    # 批次间延迟
    if batch_num > 0:  # 第一批次前不延迟
        print(f"批次处理完成，等待 {BATCH_DELAY} 秒后继续...")
        await asyncio.sleep(BATCH_DELAY)
    
    return companies

def main():
    # 加载投资公司数据
    angel_investors = load_angel_investors()
    
    if not angel_investors:
        return
    
    logger.info(f"开始处理 {len(angel_investors)} 家投资公司")
    print(f"已加载 {len(angel_investors)} 家投资公司数据")
    
    success_count = 0
    fail_count = 0
    
    # 处理所有公司，按批次处理
    total_companies = len(angel_investors)
    all_results = []
    
    # 计算总批次
    total_batches = (total_companies + BATCH_SIZE - 1) // BATCH_SIZE
    
    for i in range(0, total_companies, BATCH_SIZE):
        batch = angel_investors[i:i+BATCH_SIZE]
        batch_num = i // BATCH_SIZE + 1
        
        # 运行异步任务
        batch_results = asyncio.run(process_company_batch(batch, batch_num))
        
        # 更新统计信息
        for result in batch_results:
            if result['encyclopedia']['status'] == 'success':
                success_count += 1
            else:
                fail_count += 1
        
        all_results.extend(batch_results)
        
        # 输出进度
        progress = (batch_num / total_batches) * 100
        print(f"进度: {batch_num}/{total_batches} 批, {len(all_results)}/{total_companies} 家公司, 完成率: {progress:.2f}%")
    
    # 保存所有处理结果
    output_file = 'models/angel_investors_with_multi_encyclopedia.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n处理完成，结果保存到: {output_file}")
    print(f"成功: {success_count} 家, 失败: {fail_count} 家, 成功率: {success_count/total_companies:.2%}")
    logger.info(f"处理完成，成功: {success_count} 家, 失败: {fail_count} 家, 成功率: {success_count/total_companies:.2%}")
    
    # 分析投资领域分布
    analyze_investment_fields(all_results)

def analyze_investment_fields(investors):
    """分析投资领域分布"""
    field_stats = defaultdict(int)
    
    for investor in investors:
        if 'encyclopedia' in investor and investor['encyclopedia']['status'] == 'success':
            info = investor['encyclopedia']['info']
            
            # 尝试从不同字段获取投资领域信息
            fields = info.get('投资领域', '') or info.get('业务领域', '') or info.get('经营范围', '')
            
            if fields:
                # 分割领域（通常以逗号、分号等分隔）
                for field in re.split(r'[,，;；、]', fields):
                    field = field.strip()
                    if field:
                        field_stats[field] += 1
    
    print("\n投资领域分布:")
    for field, count in sorted(field_stats.items(), key=lambda x: x[1], reverse=True)[:15]:
        print(f"  {field}: {count}家公司")

if __name__ == "__main__":
    main()    