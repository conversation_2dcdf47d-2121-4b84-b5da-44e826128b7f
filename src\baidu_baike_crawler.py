#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
import re
import random
from urllib.parse import quote
import requests
from bs4 import BeautifulSoup
from collections import defaultdict

# 设置请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Connection': 'keep-alive',
}

def load_angel_investors(file_path='angel_investors.json'):
    """
    加载已解析的天使投资公司数据
    
    Args:
        file_path: 天使投资公司数据文件路径
        
    Returns:
        天使投资公司列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在，请先运行 parse_investor_data.py 脚本")
        return []
    except Exception as e:
        print(f"加载天使投资公司数据时出错: {e}")
        return []

def get_baidu_baike_url(company_name):
    """
    构建百度百科URL
    
    Args:
        company_name: 公司名称
        
    Returns:
        百度百科URL
    """
    encoded_name = quote(company_name)
    return f"https://baike.baidu.com/item/{encoded_name}"

def fetch_baidu_baike_page(url):
    """
    获取百度百科页面内容
    
    Args:
        url: 百度百科URL
        
    Returns:
        页面内容
    """
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except requests.exceptions.RequestException as e:
        print(f"获取页面内容时出错: {e}")
        return None

def parse_knowledge_box(html_content):
    """
    解析百度百科知识盒子
    
    Args:
        html_content: 页面HTML内容
        
    Returns:
        知识盒子中的信息
    """
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    knowledge_box = {}
    
    # 解析基本信息表格
    basic_info = soup.find('div', class_='basic-info')
    if basic_info:
        dt_elements = basic_info.find_all('dt', class_='basicInfo-item name')
        dd_elements = basic_info.find_all('dd', class_='basicInfo-item value')
        
        for dt, dd in zip(dt_elements, dd_elements):
            key = dt.get_text().strip().replace('\xa0', '')
            value = dd.get_text().strip().replace('\xa0', ' ')
            knowledge_box[key] = value
    
    # 解析摘要信息
    summary = soup.find('div', class_='lemma-summary')
    if summary:
        knowledge_box['摘要'] = summary.get_text().strip()
    
    return knowledge_box

def extract_company_info(knowledge_box):
    """
    从知识盒子中提取公司信息
    
    Args:
        knowledge_box: 知识盒子中的信息
        
    Returns:
        结构化的公司信息
    """
    company_info = {
        '成立时间': knowledge_box.get('成立时间', ''),
        '公司类型': knowledge_box.get('公司类型', ''),
        '总部地点': knowledge_box.get('总部地点', ''),
        '创始人': knowledge_box.get('创始人', ''),
        '董事长': knowledge_box.get('董事长', ''),
        '法定代表人': knowledge_box.get('法定代表人', ''),
        '注册资本': knowledge_box.get('注册资本', ''),
        '经营范围': knowledge_box.get('经营范围', ''),
        '业务领域': knowledge_box.get('业务领域', ''),
        '投资领域': knowledge_box.get('投资领域', ''),
        '摘要': knowledge_box.get('摘要', '')
    }
    
    # 清理空值
    return {k: v for k, v in company_info.items() if v}

def main():
    # 加载天使投资公司数据
    angel_investors = load_angel_investors()
    
    if not angel_investors:
        return
    
    print(f"已加载 {len(angel_investors)} 家投资公司数据")
    
    # 创建结果目录
    os.makedirs('baidu_baike_data', exist_ok=True)
    
    # 爬取百度百科信息
    enriched_investors = []
    
    # 限制爬取的公司数量，避免请求过多
    max_companies = min(20, len(angel_investors))
    
    for i, investor in enumerate(angel_investors[:max_companies]):
        company_name = investor['name']
        print(f"\n正在处理 ({i+1}/{max_companies}): {company_name}")
        
        # 构建百度百科URL
        baike_url = get_baidu_baike_url(company_name)
        print(f"百度百科URL: {baike_url}")
        
        # 获取页面内容
        html_content = fetch_baidu_baike_page(baike_url)
        
        if html_content:
            # 解析知识盒子
            knowledge_box = parse_knowledge_box(html_content)
            
            if knowledge_box:
                print(f"成功获取知识盒子信息，包含 {len(knowledge_box)} 个属性")
                
                # 提取公司信息
                company_info = extract_company_info(knowledge_box)
                
                # 添加百度百科信息
                investor['baidu_baike'] = {
                    'url': baike_url,
                    'info': company_info
                }
                
                # 保存单个公司的百度百科数据
                company_file = os.path.join('baidu_baike_data', f"{investor['id']}.json")
                with open(company_file, 'w', encoding='utf-8') as f:
                    json.dump(investor, f, ensure_ascii=False, indent=2)
                
                print(f"已保存到: {company_file}")
            else:
                print("未找到知识盒子信息")
        else:
            print("获取页面内容失败")
        
        enriched_investors.append(investor)
        
        # 随机延迟，避免请求过于频繁
        time.sleep(random.uniform(1, 3))
    
    # 保存所有爬取的数据
    output_file = 'angel_investors_with_baike.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(enriched_investors, f, ensure_ascii=False, indent=2)
    
    print(f"\n已将爬取的百度百科数据保存到: {output_file}")
    
    # 分析投资领域分布
    analyze_investment_fields(enriched_investors)

def analyze_investment_fields(investors):
    """
    分析投资领域分布
    
    Args:
        investors: 带有百度百科信息的投资公司列表
    """
    field_stats = defaultdict(int)
    
    for investor in investors:
        if 'baidu_baike' in investor and 'info' in investor['baidu_baike']:
            info = investor['baidu_baike']['info']
            
            # 尝试从不同字段获取投资领域信息
            fields = info.get('投资领域', '') or info.get('业务领域', '') or info.get('经营范围', '')
            
            if fields:
                # 分割领域（通常以逗号、分号等分隔）
                for field in re.split(r'[,，;；、]', fields):
                    field = field.strip()
                    if field:
                        field_stats[field] += 1
    
    print("\n投资领域分布:")
    for field, count in sorted(field_stats.items(), key=lambda x: x[1], reverse=True)[:15]:
        print(f"  {field}: {count}家公司")

if __name__ == "__main__":
    main()
