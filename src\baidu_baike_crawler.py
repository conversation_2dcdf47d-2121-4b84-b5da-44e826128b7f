#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import time
import re
import random
import logging
from urllib.parse import quote
import requests
from bs4 import BeautifulSoup
from collections import defaultdict

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='encyclopedia_crawler.log'
)
logger = logging.getLogger(__name__)

# 设置请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Connection': 'keep-alive',
}

# 百科URL模板
ENCYCLOPEDIA_URLS = {
    'baidu': "https://baike.baidu.com/item/{}",
    'wikipedia': "https://zh.wikipedia.org/wiki/{}",
    'so': "https://baike.so.com/doc/{}",
    'military': "https://military.baidu.com/item/{}",
    'finance': "https://finance.baidu.com/baike/{}"
}

# 搜索引擎URL
SEARCH_ENGINES = {
    'baidu': "https://www.baidu.com/s?wd={}",
    'bing': "https://cn.bing.com/search?q={}"
}

def load_angel_investors(file_path='models/angel_investors.json'):
    """加载投资公司数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"文件 {file_path} 不存在")
        return []
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return []

def fetch_page(url, retries=3):
    """通用页面获取函数，支持重试和随机User-Agent"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 11.5; rv:90.0) Gecko/20100101 Firefox/90.0'
    ]
    
    for attempt in range(retries):
        try:
            # 随机更换User-Agent
            headers = HEADERS.copy()
            headers['User-Agent'] = random.choice(user_agents)
            
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            
            # 检查是否被重定向到错误页面
            if "error.html" in response.url or "notfound" in response.url:
                raise requests.exceptions.HTTPError(f"重定向到错误页面: {response.url}")
                
            return response.text
        except requests.exceptions.RequestException as e:
            logger.warning(f"尝试 {attempt+1}/{retries} 获取页面 {url} 时出错: {e}")
            if attempt < retries - 1:
                wait_time = random.uniform(3, 7)
                logger.info(f"等待 {wait_time:.2f} 秒后重试")
                time.sleep(wait_time)
    return None

def search_encyclopedia(keyword, encyclopedia='baidu'):
    """在指定百科中搜索关键词"""
    encoded_keyword = quote(keyword)
    search_url = SEARCH_ENGINES['baidu'].format(f"{keyword} {encyclopedia}百科")
    
    logger.info(f"在{encyclopedia}百科中搜索: {keyword}")
    html_content = fetch_page(search_url)
    
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 根据不同百科调整搜索结果解析逻辑
    if encyclopedia == 'baidu':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "baike.baidu.com/item" in href:
                try:
                    response = requests.head(href, headers=HEADERS, timeout=10, allow_redirects=True)
                    return response.url
                except:
                    continue
    elif encyclopedia == 'wikipedia':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "zh.wikipedia.org/wiki" in href:
                return href
    elif encyclopedia == 'so':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "baike.so.com/doc" in href:
                return href
    
    logger.info(f"未在{encyclopedia}百科中找到 {keyword} 的链接")
    return None

def parse_baidu_baike(html_content):
    """解析百度百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 尝试新老两种知识盒子结构
    basic_info = soup.find('div', class_='basic-info J-basic-info')
    if not basic_info:
        basic_info = soup.find('div', class_='basic-info')
    
    if basic_info:
        dt_elements = basic_info.find_all('dt', class_='basicInfo-item name')
        dd_elements = basic_info.find_all('dd', class_='basicInfo-item value')
        
        for dt, dd in zip(dt_elements, dd_elements):
            key = dt.get_text(strip=True).replace('\xa0', '')
            value = dd.get_text(strip=True).replace('\xa0', ' ')
            result[key] = value
    
    # 提取摘要
    summary = soup.find('div', class_='lemma-summary') or soup.find('div', class_='J-summary')
    if summary:
        result['摘要'] = summary.get_text(strip=True)
    
    return result

def parse_wikipedia(html_content):
    """解析维基百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 解析信息框
    infobox = soup.find('table', class_='infobox')
    if infobox:
        rows = infobox.find_all('tr')
        for row in rows:
            th = row.find('th')
            td = row.find('td')
            if th and td:
                key = th.get_text(strip=True)
                value = td.get_text(strip=True)
                result[key] = value
    
    # 提取摘要
    summary = soup.find('div', class_='mw-parser-output')
    if summary and summary.p:
        result['摘要'] = summary.p.get_text(strip=True)
    
    return result

def parse_so_baike(html_content):
    """解析360百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 解析信息栏
    info_box = soup.find('div', class_='basic-info')
    if info_box:
        items = info_box.find_all('div', class_='item')
        for item in items:
            name = item.find('div', class_='name')
            value = item.find('div', class_='value')
            if name and value:
                key = name.get_text(strip=True)
                value_text = value.get_text(strip=True)
                result[key] = value_text
    
    # 提取摘要
    summary = soup.find('div', class_='summary')
    if summary:
        result['摘要'] = summary.get_text(strip=True)
    
    return result

def extract_company_info(knowledge_box, source):
    """从知识盒子中提取公司信息，适配不同百科"""
    # 字段映射字典，处理不同百科的差异
    field_mappings = {
        'baidu': {
            '成立时间': ['成立时间'],
            '公司类型': ['公司类型'],
            '总部地点': ['总部地点'],
            '创始人': ['创始人'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本'],
            '经营范围': ['经营范围'],
            '业务领域': ['业务领域', '业务范围'],
            '投资领域': ['投资领域']
        },
        'wikipedia': {
            '成立时间': ['成立时间', '成立'],
            '公司类型': ['公司类型', '企业类型'],
            '总部地点': ['总部地点', '总部所在地'],
            '创始人': ['创始人', '创立者'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本', '实收资本'],
            '经营范围': ['经营范围', '业务范围'],
            '业务领域': ['业务领域', '产业'],
            '投资领域': ['投资领域']
        },
        'so': {
            '成立时间': ['成立时间'],
            '公司类型': ['公司类型'],
            '总部地点': ['总部地点'],
            '创始人': ['创始人'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本'],
            '经营范围': ['经营范围'],
            '业务领域': ['业务领域', '业务范围'],
            '投资领域': ['投资领域']
        }
    }
    
    company_info = {}
    mapping = field_mappings.get(source, field_mappings['baidu'])
    
    for target, sources in mapping.items():
        for source_key in sources:
            if source_key in knowledge_box:
                company_info[target] = knowledge_box[source_key]
                break
    
    # 提取摘要
    if '摘要' in knowledge_box:
        company_info['摘要'] = knowledge_box['摘要']
    
    return {k: v for k, v in company_info.items() if v}

def crawl_company_info(company_name):
    """
    尝试从多个百科获取公司信息
    优先顺序: 百度百科 -> 维基百科 -> 360百科
    """
    logger.info(f"开始爬取 {company_name} 的信息")
    result = {
        'company_name': company_name,
        'sources': [],
        'info': {},
        'status': 'fail'
    }
    
    # 尝试的百科列表及对应解析函数
    encyclopedias = [
        ('baidu', parse_baidu_baike),
        ('wikipedia', parse_wikipedia),
        ('so', parse_so_baike)
    ]
    
    for source, parser in encyclopedias:
        logger.info(f"尝试从 {source} 百科获取 {company_name} 的信息")
        
        # 直接构建URL
        encoded_name = quote(company_name)
        url = ENCYCLOPEDIA_URLS[source].format(encoded_name)
        
        # 获取页面内容
        html_content = fetch_page(url)
        
        # 如果直接访问失败，尝试搜索
        if not html_content or "未找到" in html_content or "不存在" in html_content:
            logger.info(f"直接访问失败，尝试在 {source} 百科中搜索 {company_name}")
            url = search_encyclopedia(company_name, source)
            if url:
                html_content = fetch_page(url)
        
        # 解析内容
        if html_content:
            knowledge_box = parser(html_content)
            
            if knowledge_box:
                company_info = extract_company_info(knowledge_box, source)
                
                if company_info:
                    result['sources'].append({
                        'source': source,
                        'url': url,
                        'raw_data': knowledge_box
                    })
                    
                    # 合并信息，后面的百科可能补充更多信息
                    result['info'].update(company_info)
                    result['status'] = 'success'
                    logger.info(f"从 {source} 百科成功获取到 {company_name} 的信息")
                    
                    # 如果获取到关键信息，不再尝试其他百科
                    if len(company_info) >= 5:
                        break
        
        # 随机延迟，避免请求过于频繁
        time.sleep(random.uniform(2, 5))
    
    return result

def main():
    # 加载投资公司数据
    angel_investors = load_angel_investors()
    
    if not angel_investors:
        return
    
    logger.info(f"开始处理 {len(angel_investors)} 家投资公司")
    print(f"已加载 {len(angel_investors)} 家投资公司数据")
    
    # 创建结果目录
    os.makedirs('data/multi_encyclopedia_data', exist_ok=True)
    
    success_count = 0
    fail_count = 0
    
    # 处理前50家公司，可调整
    max_companies = min(50, len(angel_investors))
    
    for i, investor in enumerate(angel_investors[:max_companies]):
        company_name = investor['name']
        print(f"\n正在处理 ({i+1}/{max_companies}): {company_name}")
        
        # 爬取公司信息
        result = crawl_company_info(company_name)
        
        # 更新投资者数据
        investor['encyclopedia'] = result
        
        # 保存单个公司的数据
        company_file = os.path.join('data/multi_encyclopedia_data', f"{investor['id']}.json")
        with open(company_file, 'w', encoding='utf-8') as f:
            json.dump(investor, f, ensure_ascii=False, indent=2)
        
        if result['status'] == 'success':
            print(f"成功从 {', '.join([s['source'] for s in result['sources']])} 百科获取信息")
            success_count += 1
        else:
            print("未能从任何百科获取有效信息")
            fail_count += 1
        
        # 记录日志
        logger.info(f"{company_name} 处理完成，状态: {result['status']}")
        
        # 随机延迟，避免请求过于频繁
        wait_time = random.uniform(3, 7)
        print(f"等待 {wait_time:.2f} 秒后继续...")
        time.sleep(wait_time)
    
    # 保存所有处理结果
    output_file = 'models/angel_investors_with_multi_encyclopedia.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(angel_investors[:max_companies], f, ensure_ascii=False, indent=2)
    
    print(f"\n处理完成，结果保存到: {output_file}")
    print(f"成功: {success_count} 家, 失败: {fail_count} 家, 成功率: {success_count/max_companies:.2%}")
    logger.info(f"处理完成，成功: {success_count} 家, 失败: {fail_count} 家, 成功率: {success_count/max_companies:.2%}")
    
    # 分析投资领域分布
    analyze_investment_fields(angel_investors[:max_companies])

def analyze_investment_fields(investors):
    """分析投资领域分布"""
    field_stats = defaultdict(int)
    
    for investor in investors:
        if 'encyclopedia' in investor and investor['encyclopedia']['status'] == 'success':
            info = investor['encyclopedia']['info']
            
            # 尝试从不同字段获取投资领域信息
            fields = info.get('投资领域', '') or info.get('业务领域', '') or info.get('经营范围', '')
            
            if fields:
                # 分割领域（通常以逗号、分号等分隔）
                for field in re.split(r'[,，;；、]', fields):
                    field = field.strip()
                    if field:
                        field_stats[field] += 1
    
    print("\n投资领域分布:")
    for field, count in sorted(field_stats.items(), key=lambda x: x[1], reverse=True)[:15]:
        print(f"  {field}: {count}家公司")

if __name__ == "__main__":
    main()    