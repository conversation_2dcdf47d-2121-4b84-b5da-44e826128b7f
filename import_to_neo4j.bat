@echo off
echo 中国天使投资知识图谱 - Neo4j批量导入脚本
echo ======================================

REM 设置数据库名称
set DB_NAME=angelinvestment


echo 步骤2: 执行Neo4j批量导入
echo 正在导入数据到Neo4j...

REM 直接使用neo4j-admin命令，不需要指定完整路径
neo4j-admin import --database=%DB_NAME% ^
    --nodes=AngelInvestor=neo4j_import\angel_investor.csv ^
    --nodes=VentureCapital=neo4j_import\venture_capital.csv ^
    --nodes=StartupCompany=neo4j_import\company.csv ^
    --nodes=InvestmentRound=neo4j_import\investment_round.csv ^
    --nodes=InvestmentEvent=neo4j_import\investment_event.csv ^
    --relationships=INVESTS=neo4j_import\invests_relationship.csv ^
    --relationships=HAS_INVESTMENT_EVENT=neo4j_import\has_event_relationship.csv ^
    --relationships=HAS_ROUND=neo4j_import\has_round_relationship.csv ^
    --delimiter="," --array-delimiter=";" --id-type=STRING

if %ERRORLEVEL% NEQ 0 (
    echo 数据导入失败，请检查错误信息
    exit /b 1
)

echo.
echo 数据导入完成！
echo.
echo 请确保在neo4j.conf文件中设置：
echo dbms.default_database=%DB_NAME%
echo.
echo 然后启动Neo4j服务：
echo neo4j console
echo.
echo 或者：
echo neo4j start
echo.
echo 然后在浏览器中访问：http://localhost:7474
echo 用户名: neo4j
echo 密码: 您设置的密码
echo.

pause
