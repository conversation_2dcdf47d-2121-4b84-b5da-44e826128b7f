@echo off
echo 中国天使投资知识图谱 - Neo4j批量导入脚本
echo ======================================

REM 设置Neo4j安装路径（请根据实际情况修改）
set NEO4J_HOME=C:\path\to\neo4j

REM 设置数据库名称
set DB_NAME=angelinvestment

echo 步骤1: 转换数据为Neo4j可导入的CSV格式
python src/convert_to_neo4j_csv.py
if %ERRORLEVEL% NEQ 0 (
    echo 数据转换失败，请检查错误信息
    exit /b 1
)

echo 步骤2: 停止Neo4j服务（如果正在运行）
"%NEO4J_HOME%\bin\neo4j" stop
timeout /t 5

echo 步骤3: 删除现有数据库（如果存在）
if exist "%NEO4J_HOME%\data\databases\%DB_NAME%" (
    echo 删除现有数据库: %DB_NAME%
    rmdir /s /q "%NEO4J_HOME%\data\databases\%DB_NAME%"
)
if exist "%NEO4J_HOME%\data\transactions\%DB_NAME%" (
    echo 删除现有事务日志: %DB_NAME%
    rmdir /s /q "%NEO4J_HOME%\data\transactions\%DB_NAME%"
)

echo 步骤4: 执行Neo4j批量导入
"%NEO4J_HOME%\bin\neo4j-admin" import --database=%DB_NAME% ^
    --nodes=AngelInvestor=neo4j_import/angel_investor.csv ^
    --nodes=VentureCapital=neo4j_import/venture_capital.csv ^
    --nodes=StartupCompany=neo4j_import/company.csv ^
    --nodes=InvestmentRound=neo4j_import/investment_round.csv ^
    --nodes=InvestmentEvent=neo4j_import/investment_event.csv ^
    --relationships=INVESTS=neo4j_import/invests_relationship.csv ^
    --relationships=HAS_INVESTMENT_EVENT=neo4j_import/has_event_relationship.csv ^
    --relationships=HAS_ROUND=neo4j_import/has_round_relationship.csv ^
    --delimiter="," --array-delimiter=";" --id-type=STRING

if %ERRORLEVEL% NEQ 0 (
    echo 数据导入失败，请检查错误信息
    exit /b 1
)

echo 步骤5: 更新数据库配置
echo dbms.default_database=%DB_NAME% >> "%NEO4J_HOME%\conf\neo4j.conf"

echo 步骤6: 启动Neo4j服务
"%NEO4J_HOME%\bin\neo4j" start
timeout /t 5

echo 步骤7: 分析导入的数据
python src/analyze_neo4j_data.py

echo 导入完成！请访问 http://localhost:7474 查看导入的数据
echo 用户名: neo4j
echo 密码: 您设置的密码

pause
