digraph AngelInvestmentOntology {
    // 图形设置
    rankdir=BT;  // 自下而上的布局
    fontname="Microsoft YaHei";
    fontsize=16;
    bgcolor="white";
    node [shape=box, style=filled, fillcolor="#E8F8F5", fontname="Microsoft YaHei", fontsize=12, margin=0.2];
    edge [fontname="Microsoft YaHei", fontsize=10, arrowsize=0.8];
    
    // 标题
    label="中国天使投资知识图谱本体";
    labelloc="t";
    
    // 类定义
    Thing [label="Thing\n(事物)", fillcolor="#D6EAF8"];
    
    // 核心类
    Organization [label="Organization\n(组织)", fillcolor="#D6EAF8"];
    Person [label="Person\n(人物)", fillcolor="#D6EAF8"];
    Event [label="Event\n(事件)", fillcolor="#D6EAF8"];
    Location [label="Location\n(地点)", fillcolor="#D6EAF8"];
    InvestmentField [label="InvestmentField\n(投资领域)", fillcolor="#D6EAF8"];
    
    // 组织子类
    InvestmentInstitution [label="InvestmentInstitution\n(投资机构)", fillcolor="#AED6F1"];
    AngelInvestor [label="AngelInvestor\n(天使投资机构)", fillcolor="#85C1E9"];
    VentureCapital [label="VentureCapital\n(风险投资机构)", fillcolor="#85C1E9"];
    Company [label="Company\n(公司)", fillcolor="#AED6F1"];
    StartupCompany [label="StartupCompany\n(创业公司)", fillcolor="#85C1E9"];
    
    // 人物子类
    Investor [label="Investor\n(投资人)", fillcolor="#AED6F1"];
    Founder [label="Founder\n(创始人)", fillcolor="#AED6F1"];
    
    // 事件子类
    InvestmentEvent [label="InvestmentEvent\n(投资事件)", fillcolor="#AED6F1"];
    InvestmentRound [label="InvestmentRound\n(投资轮次)", fillcolor="#AED6F1"];
    AngelRound [label="AngelRound\n(天使轮)", fillcolor="#85C1E9"];
    RoundA [label="RoundA\n(A轮)", fillcolor="#85C1E9"];
    RoundB [label="RoundB\n(B轮)", fillcolor="#85C1E9"];
    
    // 地点子类
    City [label="City\n(城市)", fillcolor="#AED6F1"];
    Province [label="Province\n(省份)", fillcolor="#AED6F1"];
    Country [label="Country\n(国家)", fillcolor="#AED6F1"];
    
    // 投资领域子类
    Technology [label="Technology\n(科技)", fillcolor="#AED6F1"];
    Healthcare [label="Healthcare\n(医疗健康)", fillcolor="#AED6F1"];
    Education [label="Education\n(教育)", fillcolor="#AED6F1"];
    
    // 类层次关系
    Thing -> Organization;
    Thing -> Person;
    Thing -> Event;
    Thing -> Location;
    Thing -> InvestmentField;
    
    Organization -> InvestmentInstitution;
    Organization -> Company;
    
    InvestmentInstitution -> AngelInvestor;
    InvestmentInstitution -> VentureCapital;
    
    Company -> StartupCompany;
    
    Person -> Investor;
    Person -> Founder;
    
    Event -> InvestmentEvent;
    Event -> InvestmentRound;
    
    InvestmentRound -> AngelRound;
    InvestmentRound -> RoundA;
    InvestmentRound -> RoundB;
    
    Location -> City;
    Location -> Province;
    Location -> Country;
    
    InvestmentField -> Technology;
    InvestmentField -> Healthcare;
    InvestmentField -> Education;
    
    // 对象属性（关系）
    edge [color="#2E86C1", fontcolor="#2E86C1"];
    
    // 投资机构与投资人的关系
    Investor -> InvestmentInstitution [label="manages\n(管理)"];
    
    // 创始人关系
    Founder -> InvestmentInstitution [label="hasFounder\n(有创始人)", dir=back];
    Founder -> Company [label="hasFounder\n(有创始人)", dir=back];
    
    // 投资机构与创业公司的关系
    InvestmentInstitution -> Company [label="invests\n(投资)"];
    Investor -> Company [label="invests\n(投资)"];
    
    // 投资事件相关关系
    InvestmentInstitution -> InvestmentEvent [label="hasInvestmentEvent\n(有投资事件)"];
    Company -> InvestmentEvent [label="hasInvestmentEvent\n(有投资事件)"];
    InvestmentEvent -> InvestmentRound [label="hasRound\n(有轮次)"];
    
    // 地理位置关系
    InvestmentInstitution -> Location [label="locatedIn\n(位于)"];
    Company -> Location [label="locatedIn\n(位于)"];
    
    // 投资领域关系
    InvestmentInstitution -> InvestmentField [label="focusesOn\n(关注领域)"];
    
    // 数据属性
    edge [color="#27AE60", fontcolor="#27AE60", style=dashed];
    
    // 通用属性
    Thing -> Thing [label="name\n(名称)"];
    Thing -> Thing [label="description\n(描述)"];
    
    // 机构和公司属性
    InvestmentInstitution -> InvestmentInstitution [label="foundingDate\n(成立日期)"];
    Company -> Company [label="foundingDate\n(成立日期)"];
    
    // 投资事件属性
    InvestmentEvent -> InvestmentEvent [label="investmentAmount\n(投资金额)"];
    InvestmentEvent -> InvestmentEvent [label="investmentDate\n(投资日期)"];
    
    // 图例
    subgraph cluster_legend {
        label="图例";
        style=filled;
        fillcolor="#F8F9F9";
        fontsize=14;
        
        legend_class [label="类", shape=box, style=filled, fillcolor="#D6EAF8"];
        legend_subclass [label="子类", shape=box, style=filled, fillcolor="#AED6F1"];
        legend_instance [label="实例", shape=box, style=filled, fillcolor="#85C1E9"];
        legend_object_property [label="对象属性", shape=plaintext];
        legend_data_property [label="数据属性", shape=plaintext];
        
        legend_class -> legend_subclass [label="is-a", color="#2E86C1", fontcolor="#2E86C1"];
        legend_subclass -> legend_instance [label="is-a", color="#2E86C1", fontcolor="#2E86C1"];
        legend_class -> legend_object_property [label="关系", color="#2E86C1", fontcolor="#2E86C1"];
        legend_class -> legend_data_property [label="属性", color="#27AE60", fontcolor="#27AE60", style=dashed];
    }
}
