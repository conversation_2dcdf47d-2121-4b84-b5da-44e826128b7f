#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json

def generate_cypher_from_ontology(ontology_file_path):
    """
    从本体JSON文件生成Neo4j的Cypher查询语句
    
    Args:
        ontology_file_path: 本体JSON文件路径
        
    Returns:
        Cypher查询语句
    """
    try:
        # 读取本体JSON文件
        with open(ontology_file_path, 'r', encoding='utf-8') as f:
            ontology_data = json.load(f)
        
        # 提取类和属性
        classes = []
        object_properties = []
        datatype_properties = []
        
        for item in ontology_data.get('@graph', []):
            if item.get('@type') == 'owl:Class':
                classes.append({
                    'id': item.get('@id', ''),
                    'label': item.get('rdfs:label', ''),
                    'comment': item.get('rdfs:comment', ''),
                    'subClassOf': item.get('rdfs:subClassOf', {}).get('@id', '')
                })
            elif item.get('@type') == 'owl:ObjectProperty':
                object_properties.append({
                    'id': item.get('@id', ''),
                    'label': item.get('rdfs:label', ''),
                    'comment': item.get('rdfs:comment', ''),
                    'domain': item.get('rdfs:domain', ''),
                    'range': item.get('rdfs:range', '')
                })
            elif item.get('@type') == 'owl:DatatypeProperty':
                datatype_properties.append({
                    'id': item.get('@id', ''),
                    'label': item.get('rdfs:label', ''),
                    'comment': item.get('rdfs:comment', ''),
                    'domain': item.get('rdfs:domain', ''),
                    'range': item.get('rdfs:range', '')
                })
        
        # 生成Cypher查询语句
        cypher_queries = []
        
        # 1. 创建约束和索引
        cypher_queries.append("// 创建约束和索引")
        for cls in classes:
            class_name = cls['id'].split(':')[-1]
            cypher_queries.append(f"CREATE CONSTRAINT IF NOT EXISTS FOR (n:{class_name}) REQUIRE n.id IS UNIQUE;")
        cypher_queries.append("")
        
        # 2. 创建类节点
        cypher_queries.append("// 创建类节点")
        for cls in classes:
            class_id = cls['id']
            class_name = class_id.split(':')[-1]
            class_label = cls['label']
            class_comment = cls['comment']
            cypher_queries.append(f"CREATE (:{class_name} {{id: '{class_id}', name: '{class_label}', description: '{class_comment}'}});")
        cypher_queries.append("")
        
        # 3. 创建类层次关系
        cypher_queries.append("// 创建类层次关系")
        for cls in classes:
            if cls['subClassOf'] and cls['subClassOf'] != 'owl:Thing':
                class_id = cls['id']
                class_name = class_id.split(':')[-1]
                parent_id = cls['subClassOf']
                parent_name = parent_id.split(':')[-1]
                cypher_queries.append(f"MATCH (c:{class_name} {{id: '{class_id}'}}), (p:{parent_name} {{id: '{parent_id}'}}) CREATE (c)-[:IS_A]->(p);")
        cypher_queries.append("")
        
        # 4. 创建对象属性关系
        cypher_queries.append("// 创建对象属性关系模板")
        for prop in object_properties:
            prop_id = prop['id']
            prop_name = prop_id.split(':')[-1]
            prop_label = prop['label']
            prop_comment = prop['comment']
            
            # 处理定义域
            domain_classes = []
            if isinstance(prop['domain'], list):
                domain_classes = [d.get('@id', '').split(':')[-1] for d in prop['domain']]
            elif isinstance(prop['domain'], dict):
                domain_classes = [prop['domain'].get('@id', '').split(':')[-1]]
            
            # 处理值域
            range_class = ""
            if isinstance(prop['range'], dict):
                range_class = prop['range'].get('@id', '').split(':')[-1]
            
            if domain_classes and range_class:
                for domain_class in domain_classes:
                    cypher_queries.append(f"// 关系: {domain_class} -[:{prop_name}]-> {range_class}")
                    cypher_queries.append(f"// 示例: MATCH (a:{domain_class}), (b:{range_class}) WHERE ... CREATE (a)-[:{prop_name} {{name: '{prop_label}', description: '{prop_comment}'}}]->(b);")
        cypher_queries.append("")
        
        # 5. 创建数据属性
        cypher_queries.append("// 数据属性")
        for prop in datatype_properties:
            prop_id = prop['id']
            prop_name = prop_id.split(':')[-1]
            prop_label = prop['label']
            prop_comment = prop['comment']
            
            # 处理定义域
            domain_classes = []
            if isinstance(prop['domain'], list):
                domain_classes = [d.get('@id', '').split(':')[-1] for d in prop['domain']]
            elif isinstance(prop['domain'], dict):
                domain_classes = [prop['domain'].get('@id', '').split(':')[-1]]
            
            if domain_classes:
                for domain_class in domain_classes:
                    cypher_queries.append(f"// 属性: {domain_class}.{prop_name}")
                    cypher_queries.append(f"// 示例: MATCH (n:{domain_class}) SET n.{prop_name} = '值';")
        
        # 6. 示例数据
        cypher_queries.append("\n// 示例数据")
        cypher_queries.append("""
// 创建投资机构节点
CREATE (sequoia:AngelInvestor {id: 'ai:sequoia', name: '红杉资本中国', foundingDate: '2005-09-01', description: '红杉资本中国基金是全球知名风险投资机构红杉资本在中国设立的基金'});
CREATE (idg:VentureCapital {id: 'ai:idg', name: 'IDG资本', foundingDate: '1992-01-01', description: 'IDG资本是全球领先的投资机构，是最早进入中国市场的外资风险投资机构之一'});
CREATE (zhen:AngelInvestor {id: 'ai:zhen', name: '真格基金', foundingDate: '2011-01-01', description: '真格基金是中国知名的天使投资机构，专注于早期投资'});

// 创建投资人节点
CREATE (shen:Investor {id: 'ai:shen', name: '沈南鹏', position: '创始及执行合伙人', description: '红杉资本中国基金创始及执行合伙人'});
CREATE (xu:Investor {id: 'ai:xu', name: '徐小平', position: '创始人', description: '真格基金创始人'});
CREATE (xiong:Investor {id: 'ai:xiong', name: '熊晓鸽', position: '创始人', description: 'IDG资本创始人'});

// 创建创始人节点
CREATE (shen_founder:Founder {id: 'ai:shen_founder', name: '沈南鹏', description: '红杉资本中国基金创始人'});
CREATE (xu_founder:Founder {id: 'ai:xu_founder', name: '徐小平', description: '真格基金创始人'});
CREATE (xiong_founder:Founder {id: 'ai:xiong_founder', name: '熊晓鸽', description: 'IDG资本创始人'});

// 创建公司节点
CREATE (bytedance:StartupCompany {id: 'ai:bytedance', name: '字节跳动', foundingDate: '2012-03-01', description: '字节跳动是一家科技公司，旗下有抖音、今日头条等产品'});
CREATE (meituan:StartupCompany {id: 'ai:meituan', name: '美团', foundingDate: '2010-03-04', description: '美团是中国领先的生活服务电子商务平台'});
CREATE (xiaomi:StartupCompany {id: 'ai:xiaomi', name: '小米', foundingDate: '2010-04-06', description: '小米是一家以手机、智能硬件和IoT平台为核心的消费电子公司'});

// 创建投资领域节点
CREATE (tech:Technology {id: 'ai:tech', name: '科技', description: '科技领域'});
CREATE (health:Healthcare {id: 'ai:health', name: '医疗健康', description: '医疗健康领域'});
CREATE (edu:Education {id: 'ai:edu', name: '教育', description: '教育领域'});

// 创建地理位置节点
CREATE (beijing:City {id: 'ai:beijing', name: '北京', description: '中国首都'});
CREATE (shanghai:City {id: 'ai:shanghai', name: '上海', description: '中国经济中心'});

// 创建投资轮次节点
CREATE (angel:AngelRound {id: 'ai:angel', name: '天使轮', description: '天使投资轮次'});
CREATE (roundA:RoundA {id: 'ai:roundA', name: 'A轮', description: 'A轮融资'});
CREATE (roundB:RoundB {id: 'ai:roundB', name: 'B轮', description: 'B轮融资'});

// 创建投资事件节点
CREATE (event1:InvestmentEvent {id: 'ai:event1', name: '红杉资本投资字节跳动A轮', investmentAmount: 10000000, investmentDate: '2012-09-01', description: '红杉资本对字节跳动的A轮投资'});
CREATE (event2:InvestmentEvent {id: 'ai:event2', name: 'IDG资本投资小米天使轮', investmentAmount: 5000000, investmentDate: '2010-05-01', description: 'IDG资本对小米的天使轮投资'});
CREATE (event3:InvestmentEvent {id: 'ai:event3', name: '真格基金投资美团天使轮', investmentAmount: 3000000, investmentDate: '2010-04-01', description: '真格基金对美团的天使轮投资'});

// 创建关系
// 投资机构与创始人关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:Founder {id: 'ai:shen_founder'}) CREATE (a)-[:hasFounder {name: '有创始人'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:Founder {id: 'ai:xu_founder'}) CREATE (a)-[:hasFounder {name: '有创始人'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:Founder {id: 'ai:xiong_founder'}) CREATE (a)-[:hasFounder {name: '有创始人'}]->(b);

// 投资人与投资机构关系
MATCH (a:Investor {id: 'ai:shen'}), (b:AngelInvestor {id: 'ai:sequoia'}) CREATE (a)-[:manages {name: '管理'}]->(b);
MATCH (a:Investor {id: 'ai:xu'}), (b:AngelInvestor {id: 'ai:zhen'}) CREATE (a)-[:manages {name: '管理'}]->(b);
MATCH (a:Investor {id: 'ai:xiong'}), (b:VentureCapital {id: 'ai:idg'}) CREATE (a)-[:manages {name: '管理'}]->(b);

// 投资机构与投资事件关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:InvestmentEvent {id: 'ai:event1'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:InvestmentEvent {id: 'ai:event2'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:InvestmentEvent {id: 'ai:event3'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);

// 投资事件与投资轮次关系
MATCH (a:InvestmentEvent {id: 'ai:event1'}), (b:RoundA {id: 'ai:roundA'}) CREATE (a)-[:hasRound {name: '有轮次'}]->(b);
MATCH (a:InvestmentEvent {id: 'ai:event2'}), (b:AngelRound {id: 'ai:angel'}) CREATE (a)-[:hasRound {name: '有轮次'}]->(b);
MATCH (a:InvestmentEvent {id: 'ai:event3'}), (b:AngelRound {id: 'ai:angel'}) CREATE (a)-[:hasRound {name: '有轮次'}]->(b);

// 投资机构与投资领域关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:Education {id: 'ai:edu'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:Education {id: 'ai:edu'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);

// 投资机构与地理位置关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);

// 投资机构与公司关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:StartupCompany {id: 'ai:bytedance'}) CREATE (a)-[:invests {name: '投资'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:StartupCompany {id: 'ai:xiaomi'}) CREATE (a)-[:invests {name: '投资'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:StartupCompany {id: 'ai:meituan'}) CREATE (a)-[:invests {name: '投资'}]->(b);

// 公司与投资事件关系
MATCH (a:StartupCompany {id: 'ai:bytedance'}), (b:InvestmentEvent {id: 'ai:event1'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:StartupCompany {id: 'ai:xiaomi'}), (b:InvestmentEvent {id: 'ai:event2'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:StartupCompany {id: 'ai:meituan'}), (b:InvestmentEvent {id: 'ai:event3'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);

// 公司与地理位置关系
MATCH (a:StartupCompany {id: 'ai:bytedance'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:StartupCompany {id: 'ai:xiaomi'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:StartupCompany {id: 'ai:meituan'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);

// 公司与投资领域关系
MATCH (a:StartupCompany {id: 'ai:bytedance'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:belongsTo {name: '属于领域'}]->(b);
MATCH (a:StartupCompany {id: 'ai:xiaomi'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:belongsTo {name: '属于领域'}]->(b);
MATCH (a:StartupCompany {id: 'ai:meituan'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:belongsTo {name: '属于领域'}]->(b);
""")
        
        return "\n".join(cypher_queries)
    
    except Exception as e:
        print(f"生成Cypher查询语句时出错: {e}")
        return ""

def main():
    print("开始生成Neo4j的Cypher查询语句...")
    
    # 本体文件路径
    ontology_file_path = 'models/angel_investment_ontology.json'
    
    # 检查本体文件是否存在
    if not os.path.exists(ontology_file_path):
        print(f"错误: 本体文件不存在: {ontology_file_path}")
        return
    
    # 生成Cypher查询语句
    cypher_queries = generate_cypher_from_ontology(ontology_file_path)
    
    if cypher_queries:
        # 保存Cypher查询语句到文件
        output_file_path = 'models/angel_investment_ontology.cypher'
        with open(output_file_path, 'w', encoding='utf-8') as f:
            f.write(cypher_queries)
        
        print(f"已生成Cypher查询语句: {output_file_path}")
    else:
        print("生成Cypher查询语句失败")

if __name__ == "__main__":
    main()
