#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import subprocess
import argparse

def visualize_ontology(dot_file, output_format='png'):
    """
    使用Graphviz可视化本体图
    
    Args:
        dot_file: DOT文件路径
        output_format: 输出格式，支持png、svg、pdf等
    """
    # 检查DOT文件是否存在
    if not os.path.exists(dot_file):
        print(f"错误: DOT文件 {dot_file} 不存在")
        return False
    
    # 构建输出文件路径
    output_file = os.path.splitext(dot_file)[0] + f".{output_format}"
    
    # 构建Graphviz命令
    cmd = ['dot', '-T' + output_format, dot_file, '-o', output_file]
    
    try:
        # 执行命令
        subprocess.run(cmd, check=True)
        print(f"成功生成本体图: {output_file}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"生成本体图时出错: {e}")
        return False
    except FileNotFoundError:
        print("错误: 未找到Graphviz。请确保已安装Graphviz并将其添加到PATH环境变量中。")
        print("可以从 https://graphviz.org/download/ 下载安装Graphviz。")
        return False

def main():
    parser = argparse.ArgumentParser(description='可视化中国天使投资知识图谱本体')
    parser.add_argument('--dot', default='models/angel_investment_ontology_updated.dot', help='DOT文件路径')
    parser.add_argument('--format', default='png', choices=['png', 'svg', 'pdf'], help='输出格式')
    
    args = parser.parse_args()
    
    # 可视化本体图
    visualize_ontology(args.dot, args.format)

if __name__ == "__main__":
    main()
