#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j连接器
提供Neo4j数据库连接和基本操作功能
"""

from py2neo import Graph, Node, Relationship
import logging

logger = logging.getLogger(__name__)

class Neo4jConnector:
    def __init__(self, uri="bolt://localhost:7687", user="neo4j", password="angelinvestment", database=None):
        """初始化Neo4j连接"""
        self.uri = uri
        self.user = user
        self.password = password
        self.database = database
        self.graph = None
        self.connect()
    
    def connect(self):
        """连接到Neo4j数据库"""
        try:
            if self.database:
                full_uri = f"{self.uri}/{self.database}"
                self.graph = Graph(full_uri, auth=(self.user, self.password))
            else:
                self.graph = Graph(self.uri, auth=(self.user, self.password))
            
            # 测试连接
            result = self.graph.run("RETURN 1").data()
            logger.info(f"成功连接到Neo4j数据库: {self.uri}")
            return True
            
        except Exception as e:
            logger.error(f"连接Neo4j失败: {e}")
            return False
    
    def execute_query(self, query, parameters=None):
        """执行Cypher查询"""
        try:
            if parameters:
                result = self.graph.run(query, parameters)
            else:
                result = self.graph.run(query)
            return result.data()
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            return []
    
    def create_node(self, label, properties):
        """创建节点"""
        try:
            node = Node(label, **properties)
            self.graph.create(node)
            return node
        except Exception as e:
            logger.error(f"创建节点失败: {e}")
            return None
    
    def create_relationship(self, start_node, end_node, rel_type, properties=None):
        """创建关系"""
        try:
            if properties:
                rel = Relationship(start_node, rel_type, end_node, **properties)
            else:
                rel = Relationship(start_node, rel_type, end_node)
            self.graph.create(rel)
            return rel
        except Exception as e:
            logger.error(f"创建关系失败: {e}")
            return None
    
    def find_node(self, label, properties):
        """查找节点"""
        try:
            node = self.graph.nodes.match(label, **properties).first()
            return node
        except Exception as e:
            logger.error(f"查找节点失败: {e}")
            return None
    
    def clear_database(self):
        """清空数据库"""
        try:
            self.graph.run("MATCH (n) DETACH DELETE n")
            logger.info("数据库已清空")
            return True
        except Exception as e:
            logger.error(f"清空数据库失败: {e}")
            return False
    
    def get_node_count(self, label=None):
        """获取节点数量"""
        try:
            if label:
                query = f"MATCH (n:{label}) RETURN count(n) as count"
            else:
                query = "MATCH (n) RETURN count(n) as count"
            result = self.execute_query(query)
            return result[0]['count'] if result else 0
        except Exception as e:
            logger.error(f"获取节点数量失败: {e}")
            return 0
    
    def get_relationship_count(self, rel_type=None):
        """获取关系数量"""
        try:
            if rel_type:
                query = f"MATCH ()-[r:{rel_type}]->() RETURN count(r) as count"
            else:
                query = "MATCH ()-[r]->() RETURN count(r) as count"
            result = self.execute_query(query)
            return result[0]['count'] if result else 0
        except Exception as e:
            logger.error(f"获取关系数量失败: {e}")
            return 0
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        stats = {}
        
        # 节点统计
        node_query = "MATCH (n) RETURN labels(n) as labels, count(n) as count"
        node_results = self.execute_query(node_query)
        
        stats['nodes'] = {}
        for result in node_results:
            labels = result['labels']
            count = result['count']
            if labels:
                label = labels[0]
                stats['nodes'][label] = count
        
        # 关系统计
        rel_query = "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
        rel_results = self.execute_query(rel_query)
        
        stats['relationships'] = {}
        for result in rel_results:
            rel_type = result['type']
            count = result['count']
            stats['relationships'][rel_type] = count
        
        return stats
