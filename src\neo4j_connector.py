#!/usr/bin/env python
# -*- coding: utf-8 -*-

from py2neo import Graph
from typing import Dict, List, Any, Optional
from qa_state_machine import Intent, EntityType

class Neo4jConnector:
    """Neo4j数据库连接器，负责将查询意图转换为Cypher查询并执行"""
    
    def __init__(self, uri: str = "bolt://localhost:7687", user: str = "neo4j", password: str = "password"):
        """
        初始化Neo4j连接器
        
        Args:
            uri: Neo4j数据库URI
            user: 用户名
            password: 密码
        """
        self.uri = uri
        self.user = user
        self.password = password
        self.graph = None
    
    def connect(self) -> bool:
        """
        连接到Neo4j数据库
        
        Returns:
            连接是否成功
        """
        try:
            self.graph = Graph(self.uri, auth=(self.user, self.password))
            return True
        except Exception as e:
            print(f"连接Neo4j数据库失败: {e}")
            return False
    
    def execute_query(self, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行查询
        
        Args:
            query_info: 查询信息，包含实体类型、实体名称、查询意图和参数
            
        Returns:
            查询结果
        """
        if not self.graph:
            if not self.connect():
                return {'status': 'error', 'message': '无法连接到数据库'}
        
        entity_type = query_info.get('entity_type')
        entity_name = query_info.get('entity_name')
        intent = query_info.get('intent')
        parameters = query_info.get('parameters', {})
        
        # 根据实体类型和查询意图生成Cypher查询
        cypher_query, params = self._generate_cypher_query(entity_type, entity_name, intent, parameters)
        
        if not cypher_query:
            return {'status': 'error', 'message': '无法生成查询语句'}
        
        try:
            # 执行查询
            result = self.graph.run(cypher_query, **params).data()
            return {'status': 'success', 'data': result, 'query': cypher_query, 'params': params}
        except Exception as e:
            return {'status': 'error', 'message': f'查询执行失败: {e}', 'query': cypher_query, 'params': params}
    
    def _generate_cypher_query(self, entity_type: EntityType, entity_name: str, intent: Intent, parameters: Dict[str, Any]) -> tuple:
        """
        生成Cypher查询
        
        Args:
            entity_type: 实体类型
            entity_name: 实体名称
            intent: 查询意图
            parameters: 查询参数
            
        Returns:
            Cypher查询语句和参数
        """
        # 投资机构相关查询
        if entity_type == EntityType.INVESTOR:
            return self._generate_investor_query(entity_name, intent, parameters)
        # 创业公司相关查询
        elif entity_type == EntityType.COMPANY:
            return self._generate_company_query(entity_name, intent, parameters)
        else:
            return None, {}
    
    def _generate_investor_query(self, investor_name: str, intent: Intent, parameters: Dict[str, Any]) -> tuple:
        """
        生成投资机构相关的Cypher查询
        
        Args:
            investor_name: 投资机构名称
            intent: 查询意图
            parameters: 查询参数
            
        Returns:
            Cypher查询语句和参数
        """
        params = {'investor_name': investor_name}
        
        if intent == Intent.INVESTOR_PORTFOLIO:
            # 查询投资机构的投资组合
            query = """
            MATCH (i)-[:INVESTS]->(c:StartupCompany)
            WHERE i.name = $investor_name
            RETURN c.name AS company_name, c.foundingDate AS founding_date, c.description AS description
            ORDER BY company_name
            """
            return query, params
        
        elif intent == Intent.INVESTOR_ROUNDS:
            # 查询投资机构的投资轮次分布
            query = """
            MATCH (i)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)-[:HAS_ROUND]->(r)
            WHERE i.name = $investor_name
            RETURN r.name AS round_name, count(*) AS count
            ORDER BY count DESC
            """
            return query, params
        
        elif intent == Intent.INVESTOR_FIELDS:
            # 查询投资机构的投资领域
            query = """
            MATCH (i)-[:INVESTS]->(c:StartupCompany)-[:BELONGS_TO]->(f)
            WHERE i.name = $investor_name
            RETURN f.name AS field_name, count(*) AS count
            ORDER BY count DESC
            """
            return query, params
        
        elif intent == Intent.INVESTOR_SPECIFIC_ROUND:
            # 查询投资机构的特定轮次投资
            round_name = parameters.get('round', '')
            if not round_name:
                return None, {}
            
            params['round_name'] = round_name
            query = """
            MATCH (i)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)-[:HAS_ROUND]->(r)
            MATCH (e)-[:INVOLVES]->(c:StartupCompany)
            WHERE i.name = $investor_name AND r.name = $round_name
            RETURN c.name AS company_name, e.investmentDate AS investment_date, e.investmentAmount AS investment_amount
            ORDER BY investment_date DESC
            """
            return query, params
        
        elif intent == Intent.INVESTOR_CO_INVESTMENT:
            # 查询投资机构的共同投资
            co_investor = parameters.get('co_investor', '')
            if not co_investor:
                return None, {}
            
            params['co_investor'] = co_investor
            query = """
            MATCH (i1)-[:INVESTS]->(c:StartupCompany)<-[:INVESTS]-(i2)
            WHERE i1.name = $investor_name AND i2.name = $co_investor
            RETURN c.name AS company_name, c.foundingDate AS founding_date, c.description AS description
            ORDER BY company_name
            """
            return query, params
        
        return None, {}
    
    def _generate_company_query(self, company_name: str, intent: Intent, parameters: Dict[str, Any]) -> tuple:
        """
        生成创业公司相关的Cypher查询
        
        Args:
            company_name: 创业公司名称
            intent: 查询意图
            parameters: 查询参数
            
        Returns:
            Cypher查询语句和参数
        """
        params = {'company_name': company_name}
        
        if intent == Intent.COMPANY_FUNDING_HISTORY:
            # 查询创业公司的融资历史
            query = """
            MATCH (c:StartupCompany)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)-[:HAS_ROUND]->(r)
            MATCH (i)-[:HAS_INVESTMENT_EVENT]->(e)
            WHERE c.name = $company_name
            RETURN r.name AS round_name, i.name AS investor_name, e.investmentDate AS investment_date, e.investmentAmount AS investment_amount
            ORDER BY investment_date
            """
            return query, params
        
        elif intent == Intent.COMPANY_SPECIFIC_ROUND:
            # 查询创业公司的特定轮次融资
            round_name = parameters.get('round', '')
            if not round_name:
                return None, {}
            
            params['round_name'] = round_name
            query = """
            MATCH (c:StartupCompany)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)-[:HAS_ROUND]->(r)
            MATCH (i)-[:HAS_INVESTMENT_EVENT]->(e)
            WHERE c.name = $company_name AND r.name = $round_name
            RETURN i.name AS investor_name, e.investmentDate AS investment_date, e.investmentAmount AS investment_amount
            ORDER BY investment_date
            """
            return query, params
        
        elif intent == Intent.COMPANY_TOTAL_FUNDING:
            # 查询创业公司的融资总额
            start_round = parameters.get('start_round', '')
            end_round = parameters.get('end_round', '')
            
            if start_round and end_round:
                params['start_round'] = start_round
                params['end_round'] = end_round
                query = """
                MATCH (c:StartupCompany)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)-[:HAS_ROUND]->(r)
                WHERE c.name = $company_name AND r.name IN [$start_round, $end_round]
                RETURN sum(e.investmentAmount) AS total_funding
                """
            else:
                query = """
                MATCH (c:StartupCompany)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)
                WHERE c.name = $company_name
                RETURN sum(e.investmentAmount) AS total_funding
                """
            
            return query, params
        
        elif intent == Intent.COMPANY_EARLY_INVESTORS:
            # 查询创业公司的早期投资方
            query = """
            MATCH (c:StartupCompany)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)-[:HAS_ROUND]->(r)
            MATCH (i)-[:HAS_INVESTMENT_EVENT]->(e)
            WHERE c.name = $company_name AND r.name IN ['天使轮', '种子轮', 'Pre-A轮']
            RETURN i.name AS investor_name, r.name AS round_name, e.investmentDate AS investment_date
            ORDER BY investment_date
            """
            return query, params
        
        elif intent == Intent.COMPANY_COMMON_INVESTORS:
            # 查询创业公司的共同投资方
            co_company = parameters.get('co_company', '')
            if not co_company:
                return None, {}
            
            params['co_company'] = co_company
            query = """
            MATCH (c1:StartupCompany)<-[:INVESTS]-(i)-[:INVESTS]->(c2:StartupCompany)
            WHERE c1.name = $company_name AND c2.name = $co_company
            RETURN i.name AS investor_name, i.description AS description
            ORDER BY investor_name
            """
            return query, params
        
        return None, {}
    
    def format_result(self, result: Dict[str, Any], query_info: Dict[str, Any]) -> str:
        """
        格式化查询结果
        
        Args:
            result: 查询结果
            query_info: 查询信息
            
        Returns:
            格式化后的结果字符串
        """
        if result.get('status') != 'success':
            return f"查询失败: {result.get('message', '未知错误')}"
        
        data = result.get('data', [])
        if not data:
            return "没有找到相关数据。"
        
        entity_type = query_info.get('entity_type')
        entity_name = query_info.get('entity_name')
        intent = query_info.get('intent')
        
        # 根据查询意图格式化结果
        if intent == Intent.INVESTOR_PORTFOLIO:
            return self._format_investor_portfolio(entity_name, data)
        elif intent == Intent.INVESTOR_ROUNDS:
            return self._format_investor_rounds(entity_name, data)
        elif intent == Intent.INVESTOR_FIELDS:
            return self._format_investor_fields(entity_name, data)
        elif intent == Intent.INVESTOR_SPECIFIC_ROUND:
            round_name = query_info.get('parameters', {}).get('round', '')
            return self._format_investor_specific_round(entity_name, round_name, data)
        elif intent == Intent.INVESTOR_CO_INVESTMENT:
            co_investor = query_info.get('parameters', {}).get('co_investor', '')
            return self._format_investor_co_investment(entity_name, co_investor, data)
        elif intent == Intent.COMPANY_FUNDING_HISTORY:
            return self._format_company_funding_history(entity_name, data)
        elif intent == Intent.COMPANY_SPECIFIC_ROUND:
            round_name = query_info.get('parameters', {}).get('round', '')
            return self._format_company_specific_round(entity_name, round_name, data)
        elif intent == Intent.COMPANY_TOTAL_FUNDING:
            return self._format_company_total_funding(entity_name, data)
        elif intent == Intent.COMPANY_EARLY_INVESTORS:
            return self._format_company_early_investors(entity_name, data)
        elif intent == Intent.COMPANY_COMMON_INVESTORS:
            co_company = query_info.get('parameters', {}).get('co_company', '')
            return self._format_company_common_investors(entity_name, co_company, data)
        else:
            return f"查询结果: {data}"
    
    # 以下是各种格式化函数，根据不同的查询意图格式化结果
    
    def _format_investor_portfolio(self, investor_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化投资机构的投资组合结果"""
        result = f"{investor_name}投资了以下{len(data)}家公司：\n\n"
        for i, item in enumerate(data, 1):
            company_name = item.get('company_name', '未知')
            founding_date = item.get('founding_date', '未知')
            description = item.get('description', '无描述')
            result += f"{i}. {company_name}"
            if founding_date != '未知':
                result += f"（成立于{founding_date}）"
            result += f"\n   {description}\n"
        return result
    
    def _format_investor_rounds(self, investor_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化投资机构的投资轮次分布结果"""
        result = f"{investor_name}的投资轮次分布：\n\n"
        total = sum(item.get('count', 0) for item in data)
        for item in data:
            round_name = item.get('round_name', '未知')
            count = item.get('count', 0)
            percentage = (count / total) * 100 if total > 0 else 0
            result += f"{round_name}: {count}次投资 ({percentage:.1f}%)\n"
        return result
    
    def _format_investor_fields(self, investor_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化投资机构的投资领域结果"""
        result = f"{investor_name}主要投资以下领域：\n\n"
        for item in data:
            field_name = item.get('field_name', '未知')
            count = item.get('count', 0)
            result += f"{field_name}: {count}家公司\n"
        return result
    
    def _format_investor_specific_round(self, investor_name: str, round_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化投资机构的特定轮次投资结果"""
        result = f"{investor_name}在{round_name}投资了以下{len(data)}家公司：\n\n"
        for i, item in enumerate(data, 1):
            company_name = item.get('company_name', '未知')
            investment_date = item.get('investment_date', '未知')
            investment_amount = item.get('investment_amount', '未知')
            result += f"{i}. {company_name}"
            if investment_date != '未知':
                result += f"（投资日期：{investment_date}）"
            if investment_amount != '未知':
                result += f"\n   投资金额：{investment_amount}"
            result += "\n"
        return result
    
    def _format_investor_co_investment(self, investor_name: str, co_investor: str, data: List[Dict[str, Any]]) -> str:
        """格式化投资机构的共同投资结果"""
        result = f"{investor_name}和{co_investor}共同投资了以下{len(data)}家公司：\n\n"
        for i, item in enumerate(data, 1):
            company_name = item.get('company_name', '未知')
            founding_date = item.get('founding_date', '未知')
            description = item.get('description', '无描述')
            result += f"{i}. {company_name}"
            if founding_date != '未知':
                result += f"（成立于{founding_date}）"
            result += f"\n   {description}\n"
        return result
    
    def _format_company_funding_history(self, company_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化创业公司的融资历史结果"""
        result = f"{company_name}的融资历史：\n\n"
        for i, item in enumerate(data, 1):
            round_name = item.get('round_name', '未知')
            investor_name = item.get('investor_name', '未知')
            investment_date = item.get('investment_date', '未知')
            investment_amount = item.get('investment_amount', '未知')
            result += f"{i}. {round_name}（投资方：{investor_name}）"
            if investment_date != '未知':
                result += f"\n   投资日期：{investment_date}"
            if investment_amount != '未知':
                result += f"\n   投资金额：{investment_amount}"
            result += "\n"
        return result
    
    def _format_company_specific_round(self, company_name: str, round_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化创业公司的特定轮次融资结果"""
        result = f"{company_name}的{round_name}融资情况：\n\n"
        for i, item in enumerate(data, 1):
            investor_name = item.get('investor_name', '未知')
            investment_date = item.get('investment_date', '未知')
            investment_amount = item.get('investment_amount', '未知')
            result += f"{i}. 投资方：{investor_name}"
            if investment_date != '未知':
                result += f"\n   投资日期：{investment_date}"
            if investment_amount != '未知':
                result += f"\n   投资金额：{investment_amount}"
            result += "\n"
        return result
    
    def _format_company_total_funding(self, company_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化创业公司的融资总额结果"""
        if not data:
            return f"{company_name}的融资总额数据不可用。"
        
        total_funding = data[0].get('total_funding', 0)
        return f"{company_name}的融资总额为：{total_funding}元"
    
    def _format_company_early_investors(self, company_name: str, data: List[Dict[str, Any]]) -> str:
        """格式化创业公司的早期投资方结果"""
        result = f"{company_name}的早期投资方：\n\n"
        for i, item in enumerate(data, 1):
            investor_name = item.get('investor_name', '未知')
            round_name = item.get('round_name', '未知')
            investment_date = item.get('investment_date', '未知')
            result += f"{i}. {investor_name}（{round_name}）"
            if investment_date != '未知':
                result += f"\n   投资日期：{investment_date}"
            result += "\n"
        return result
    
    def _format_company_common_investors(self, company_name: str, co_company: str, data: List[Dict[str, Any]]) -> str:
        """格式化创业公司的共同投资方结果"""
        result = f"{company_name}和{co_company}的共同投资方（{len(data)}家）：\n\n"
        for i, item in enumerate(data, 1):
            investor_name = item.get('investor_name', '未知')
            description = item.get('description', '无描述')
            result += f"{i}. {investor_name}\n   {description}\n"
        return result
