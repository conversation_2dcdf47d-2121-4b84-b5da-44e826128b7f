import os
import json
import csv
import re
from collections import defaultdict
import uuid

# 创建输出目录
def create_output_dir(output_dir):
    """创建输出目录"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")

# 清理字符串，确保CSV格式正确
def clean_string(s):
    """清理字符串，确保CSV格式正确"""
    if s is None:
        return ""
    return str(s).replace('"', '""').replace('\n', ' ').replace('\r', ' ').strip()

# 生成唯一ID
def generate_id(prefix, name):
    """生成唯一ID"""
    # 使用名称的拼音或英文作为ID的一部分
    clean_name = re.sub(r'[^\w]', '', name)
    # 如果名称为空，使用UUID
    if not clean_name:
        clean_name = str(uuid.uuid4())[:8]
    return f"{prefix}_{clean_name}"

# 从JSON数据中提取投资机构
def extract_investment_institutions(data, output_dir):
    """从JSON数据中提取投资机构"""
    # 创建CSV文件
    angel_investor_file = os.path.join(output_dir, 'angel_investor.csv')
    venture_capital_file = os.path.join(output_dir, 'venture_capital.csv')

    # 定义CSV字段
    fields = ['id:ID', 'name', 'foundingDate', 'description', ':LABEL']

    # 创建CSV写入器
    with open(angel_investor_file, 'w', newline='', encoding='utf-8') as angel_f, \
         open(venture_capital_file, 'w', newline='', encoding='utf-8') as vc_f:

        angel_writer = csv.DictWriter(angel_f, fieldnames=fields)
        vc_writer = csv.DictWriter(vc_f, fieldnames=fields)

        angel_writer.writeheader()
        vc_writer.writeheader()

        # 遍历数据中的所有节点
        for node in data:
            if '@type' in node and node['@type'] == 'investor':
                # 提取投资者名称
                if 'name' in node:
                    investor_name = node['name']
                    investor_id = node.get('@id', generate_id('investor', investor_name))

                    # 提取描述信息
                    description = ""
                    if 'description' in node:
                        description = node['description']

                    # 提取成立日期
                    founding_date = ""
                    if 'foundingDate' in node:
                        founding_date = node['foundingDate']

                    # 根据投资轮次判断是天使投资机构还是风险投资机构
                    is_angel = False
                    if 'relationship' in node and 'investCompany' in node['relationship']:
                        for company in node['relationship']['investCompany']:
                            if 'round' in company and company['round'] in ['天使轮', '种子轮', 'Pre-A轮']:
                                is_angel = True
                                break

                    # 写入相应的CSV文件
                    if is_angel:
                        angel_writer.writerow({
                            'id:ID': investor_id,
                            'name': clean_string(investor_name),
                            'foundingDate': clean_string(founding_date),
                            'description': clean_string(description),
                            ':LABEL': 'AngelInvestor'
                        })
                    else:
                        vc_writer.writerow({
                            'id:ID': investor_id,
                            'name': clean_string(investor_name),
                            'foundingDate': clean_string(founding_date),
                            'description': clean_string(description),
                            ':LABEL': 'VentureCapital'
                        })

    print(f"投资机构数据已导出到: {angel_investor_file} 和 {venture_capital_file}")
    return angel_investor_file, venture_capital_file

# 从JSON数据中提取公司
def extract_companies(data, output_dir):
    """从JSON数据中提取公司"""
    # 创建CSV文件
    company_file = os.path.join(output_dir, 'company.csv')

    # 定义CSV字段
    fields = ['id:ID', 'name', 'foundingDate', 'description', ':LABEL']

    # 创建CSV写入器
    with open(company_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fields)
        writer.writeheader()

        # 遍历数据中的所有节点
        companies = set()  # 用于去重
        for node in data:
            if '@type' in node and node['@type'] == 'investor' and 'relationship' in node and 'investCompany' in node['relationship']:
                for company in node['relationship']['investCompany']:
                    if '@type' in company and company['@type'] == 'company' and 'name' in company:
                        company_name = company['name']
                        company_id = company.get('@id', generate_id('company', company_name))

                        # 避免重复
                        if company_id in companies:
                            continue
                        companies.add(company_id)

                        # 提取描述信息
                        description = ""
                        if 'description' in company:
                            description = company['description']

                        # 提取成立日期
                        founding_date = ""
                        if 'foundingDate' in company:
                            founding_date = company['foundingDate']

                        # 写入CSV文件
                        writer.writerow({
                            'id:ID': company_id,
                            'name': clean_string(company_name),
                            'foundingDate': clean_string(founding_date),
                            'description': clean_string(description),
                            ':LABEL': 'StartupCompany'
                        })

    print(f"公司数据已导出到: {company_file}")
    return company_file

# 从JSON数据中提取投资轮次
def extract_investment_rounds(data, output_dir):
    """从JSON数据中提取投资轮次"""
    # 创建CSV文件
    round_file = os.path.join(output_dir, 'investment_round.csv')

    # 定义CSV字段
    fields = ['id:ID', 'name', 'description', ':LABEL']

    # 创建CSV写入器
    with open(round_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fields)
        writer.writeheader()

        # 收集所有轮次
        rounds = set()
        for node in data:
            if '@type' in node and node['@type'] == 'investor' and 'relationship' in node and 'investCompany' in node['relationship']:
                for company in node['relationship']['investCompany']:
                    if 'round' in company:
                        rounds.add(company['round'])

        # 轮次映射到标签
        round_labels = {
            '天使轮': 'AngelRound',
            '种子轮': 'SeedRound',
            'Pre-A轮': 'PreARound',
            'A轮': 'RoundA',
            'A+轮': 'RoundAPlus',
            'B轮': 'RoundB',
            'B+轮': 'RoundBPlus',
            'C轮': 'RoundC',
            'D轮': 'RoundD',
            'E轮': 'RoundE',
            'F轮': 'RoundF',
            'Pre-IPO': 'PreIPO',
            '新三板定增': 'NEEQ',
            '战略投资': 'StrategicInvestment',
            '并购': 'Acquisition'
        }

        # 写入CSV文件
        for round_name in rounds:
            round_id = generate_id('round', round_name)
            label = round_labels.get(round_name, 'InvestmentRound')

            writer.writerow({
                'id:ID': round_id,
                'name': clean_string(round_name),
                'description': f"{round_name}融资",
                ':LABEL': label
            })

    print(f"投资轮次数据已导出到: {round_file}")
    return round_file

# 从JSON数据中提取投资事件
def extract_investment_events(data, output_dir):
    """从JSON数据中提取投资事件"""
    # 创建CSV文件
    event_file = os.path.join(output_dir, 'investment_event.csv')

    # 定义CSV字段
    fields = ['id:ID', 'name', 'investmentAmount', 'investmentDate', 'description', ':LABEL']

    # 创建CSV写入器
    with open(event_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fields)
        writer.writeheader()

        # 遍历数据中的所有节点
        event_id = 1
        for node in data:
            if '@type' in node and node['@type'] == 'investor' and 'relationship' in node and 'investCompany' in node['relationship']:
                investor_name = node.get('name', '')
                investor_id = node.get('@id', generate_id('investor', investor_name))

                for company in node['relationship']['investCompany']:
                    if '@type' in company and company['@type'] == 'company':
                        company_name = company.get('name', '')
                        company_id = company.get('@id', generate_id('company', company_name))

                        # 提取轮次和日期
                        round_name = company.get('round', '')
                        date = company.get('date', '')

                        # 生成事件ID和名称
                        event_unique_id = f"event_{event_id}"
                        event_name = f"{investor_name}投资{company_name}{round_name}"

                        # 写入CSV文件
                        writer.writerow({
                            'id:ID': event_unique_id,
                            'name': clean_string(event_name),
                            'investmentAmount': '',  # 数据中可能没有金额信息
                            'investmentDate': clean_string(date),
                            'description': f"{investor_name}对{company_name}的{round_name}投资",
                            ':LABEL': 'InvestmentEvent'
                        })

                        event_id += 1

    print(f"投资事件数据已导出到: {event_file}")
    return event_file

# 从JSON数据中提取投资关系
def extract_investment_relationships(data, output_dir):
    """从JSON数据中提取投资关系"""
    # 创建CSV文件
    invests_file = os.path.join(output_dir, 'invests_relationship.csv')
    has_event_file = os.path.join(output_dir, 'has_event_relationship.csv')
    has_round_file = os.path.join(output_dir, 'has_round_relationship.csv')

    # 定义CSV字段
    invests_fields = [':START_ID', ':END_ID', 'name', ':TYPE']
    has_event_fields = [':START_ID', ':END_ID', 'name', ':TYPE']
    has_round_fields = [':START_ID', ':END_ID', 'name', ':TYPE']

    # 创建CSV写入器
    with open(invests_file, 'w', newline='', encoding='utf-8') as invests_f, \
         open(has_event_file, 'w', newline='', encoding='utf-8') as has_event_f, \
         open(has_round_file, 'w', newline='', encoding='utf-8') as has_round_f:

        invests_writer = csv.DictWriter(invests_f, fieldnames=invests_fields)
        has_event_writer = csv.DictWriter(has_event_f, fieldnames=has_event_fields)
        has_round_writer = csv.DictWriter(has_round_f, fieldnames=has_round_fields)

        invests_writer.writeheader()
        has_event_writer.writeheader()
        has_round_writer.writeheader()

        # 遍历数据中的所有节点
        event_id = 1
        for node in data:
            if '@type' in node and node['@type'] == 'investor' and 'relationship' in node and 'investCompany' in node['relationship']:
                investor_name = node.get('name', '')
                investor_id = node.get('@id', generate_id('investor', investor_name))

                for company in node['relationship']['investCompany']:
                    if '@type' in company and company['@type'] == 'company':
                        company_name = company.get('name', '')
                        company_id = company.get('@id', generate_id('company', company_name))

                        # 提取轮次和日期
                        round_name = company.get('round', '')
                        round_id = generate_id('round', round_name)

                        # 生成事件ID
                        event_unique_id = f"event_{event_id}"

                        # 写入投资关系
                        invests_writer.writerow({
                            ':START_ID': investor_id,
                            ':END_ID': company_id,
                            'name': '投资',
                            ':TYPE': 'INVESTS'
                        })

                        # 写入投资机构与投资事件的关系
                        has_event_writer.writerow({
                            ':START_ID': investor_id,
                            ':END_ID': event_unique_id,
                            'name': '有投资事件',
                            ':TYPE': 'HAS_INVESTMENT_EVENT'
                        })

                        # 写入公司与投资事件的关系
                        has_event_writer.writerow({
                            ':START_ID': company_id,
                            ':END_ID': event_unique_id,
                            'name': '有投资事件',
                            ':TYPE': 'HAS_INVESTMENT_EVENT'
                        })

                        # 写入投资事件与轮次的关系
                        if round_name:
                            has_round_writer.writerow({
                                ':START_ID': event_unique_id,
                                ':END_ID': round_id,
                                'name': '有轮次',
                                ':TYPE': 'HAS_ROUND'
                            })

                        event_id += 1

    print(f"投资关系数据已导出到: {invests_file}, {has_event_file}, {has_round_file}")
    return invests_file, has_event_file, has_round_file

# 主函数
def main():
    # 输入和输出目录
    input_file = 'data\invest-on-invent-KG.json'
    output_dir = 'neo4j_import'

    # 创建输出目录
    create_output_dir(output_dir)

    # 加载JSON数据
    print(f"正在加载数据文件: {input_file}")
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except UnicodeDecodeError:
        # 尝试使用不同的编码
        with open(input_file, 'r', encoding='gbk') as f:
            data = json.load(f)

    # 检查数据格式
    if isinstance(data, list):
        # 如果数据是列表，直接使用
        print("数据格式为列表，直接处理...")
    elif isinstance(data, dict) and '@graph' in data:
        # 如果数据是字典且包含@graph字段，提取@graph
        print("数据格式为字典，提取@graph字段...")
        data = data.get('@graph', [])
    else:
        print("警告: 数据格式不符合预期，尝试继续处理...")

    # 提取数据并转换为CSV
    extract_investment_institutions(data, output_dir)
    extract_companies(data, output_dir)
    extract_investment_rounds(data, output_dir)
    extract_investment_events(data, output_dir)
    extract_investment_relationships(data, output_dir)

    print("数据转换完成！")


if __name__ == "__main__":
    main()
