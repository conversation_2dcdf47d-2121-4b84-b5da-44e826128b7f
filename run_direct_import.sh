#!/bin/bash

echo "中国天使投资知识图谱 - Neo4j直接导入工具"
echo "======================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python环境，请安装Python 3.6+"
    exit 1
fi

# 检查py2neo库
python3 -c "import py2neo" &> /dev/null
if [ $? -ne 0 ]; then
    echo "正在安装py2neo库..."
    pip3 install py2neo
    if [ $? -ne 0 ]; then
        echo "安装py2neo失败，请手动安装: pip3 install py2neo"
        exit 1
    fi
fi

echo
echo "请输入Neo4j数据库连接信息:"
read -p "Neo4j URI [bolt://localhost:7687]: " uri
read -p "用户名 [neo4j]: " user
read -p "密码 [password]: " password

# 设置默认值
uri=${uri:-bolt://localhost:7687}
user=${user:-neo4j}
password=${password:-password}

echo
echo "请选择导入选项:"
echo "1. 导入前清空数据库"
echo "2. 保留现有数据"
read -p "请输入选项 (1-2): " clear_option

if [ "$clear_option" = "1" ]; then
    echo "执行导入（清空数据库）..."
    python3 src/direct_import_to_neo4j.py --uri="$uri" --user="$user" --password="$password" --clear
else
    echo "执行导入（保留现有数据）..."
    python3 src/direct_import_to_neo4j.py --uri="$uri" --user="$user" --password="$password"
fi

if [ $? -ne 0 ]; then
    echo "导入过程中出现错误，请查看日志文件 neo4j_import.log"
else
    echo
    echo "导入过程完成!"
    echo "详细日志保存在 neo4j_import.log"
fi

read -p "按任意键继续..." key
