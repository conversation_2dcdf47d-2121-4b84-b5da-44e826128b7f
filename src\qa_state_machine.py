#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
中国天使投资智能问答系统 - 有限状态自动转移图实现
基于状态机模式实现智能问答系统的状态转移逻辑
"""

import re
import json
from enum import Enum
from typing import Dict, List, Optional, Tuple
from neo4j_connector import Neo4jConnector

class QueryState(Enum):
    """查询状态枚举"""
    INITIAL = "初始状态"           # S0: 系统启动状态
    ENTITY_RECOGNITION = "实体识别"  # S1: 识别查询实体
    INTENT_ANALYSIS = "意图分析"     # S2: 分析用户意图
    QUERY_GENERATION = "查询生成"    # S3: 生成SPARQL查询
    RESULT_PROCESSING = "结果处理"   # S4: 处理查询结果
    RESPONSE_GENERATION = "响应生成" # S5: 生成自然语言回答
    ERROR_HANDLING = "错误处理"      # S6: 处理错误情况
    END = "结束状态"              # S7: 查询完成

class QueryIntent(Enum):
    """查询意图类型"""
    INVESTOR_INFO = "投资机构信息查询"
    COMPANY_INFO = "公司信息查询"
    INVESTMENT_RELATION = "投资关系查询"
    STATISTICAL_ANALYSIS = "统计分析查询"
    NETWORK_ANALYSIS = "网络分析查询"
    UNKNOWN = "未知意图"

class AngelInvestmentQAStateMachine:
    """天使投资智能问答系统状态机"""
    
    def __init__(self, neo4j_connector: Neo4jConnector):
        self.neo4j = neo4j_connector
        self.current_state = QueryState.INITIAL
        self.context = {}  # 存储查询上下文信息
        self.entities = {}  # 存储识别的实体
        self.intent = None  # 用户意图
        self.query_result = None  # 查询结果
        
        # 定义状态转移表
        self.state_transitions = {
            QueryState.INITIAL: [QueryState.ENTITY_RECOGNITION, QueryState.ERROR_HANDLING],
            QueryState.ENTITY_RECOGNITION: [QueryState.INTENT_ANALYSIS, QueryState.ERROR_HANDLING],
            QueryState.INTENT_ANALYSIS: [QueryState.QUERY_GENERATION, QueryState.ERROR_HANDLING],
            QueryState.QUERY_GENERATION: [QueryState.RESULT_PROCESSING, QueryState.ERROR_HANDLING],
            QueryState.RESULT_PROCESSING: [QueryState.RESPONSE_GENERATION, QueryState.ERROR_HANDLING],
            QueryState.RESPONSE_GENERATION: [QueryState.END, QueryState.ERROR_HANDLING],
            QueryState.ERROR_HANDLING: [QueryState.END],
            QueryState.END: []
        }
        
        # 实体识别模式
        self.entity_patterns = {
            'investor': [
                r'([\u4e00-\u9fa5]+(?:基金|投资|资本|创投|风投))',
                r'([\u4e00-\u9fa5]+(?:天使|种子|早期)投资)',
            ],
            'company': [
                r'([\u4e00-\u9fa5]+(?:科技|网络|信息|软件|技术)(?:有限公司|股份有限公司|公司)?)',
                r'([\u4e00-\u9fa5]+(?:有限公司|股份有限公司|公司))',
            ],
            'round': [
                r'([A-F]轮|天使轮|种子轮|Pre-[A-Z]轮|战略投资|并购)',
            ],
            'location': [
                r'(北京|上海|深圳|杭州|广州|成都|西安|武汉|南京|苏州)(?:市)?',
            ],
            'industry': [
                r'(互联网|人工智能|大数据|云计算|区块链|金融科技|医疗健康|教育|电商)',
            ]
        }
        
        # 意图识别关键词
        self.intent_keywords = {
            QueryIntent.INVESTOR_INFO: ['投资机构', '基金', '投资公司', '创投', '风投'],
            QueryIntent.COMPANY_INFO: ['公司', '企业', '创业公司', '被投公司'],
            QueryIntent.INVESTMENT_RELATION: ['投资', '投了', '投资关系', '投资事件'],
            QueryIntent.STATISTICAL_ANALYSIS: ['统计', '分析', '数量', '排名', '分布'],
            QueryIntent.NETWORK_ANALYSIS: ['网络', '关系', '连接', '度中心性']
        }

    def process_query(self, user_input: str) -> str:
        """
        处理用户查询的主要方法
        实现状态机的完整流程
        """
        print(f"\n🤖 开始处理查询: {user_input}")
        self.reset_context()
        self.context['user_input'] = user_input
        
        try:
            # 状态机主循环
            while self.current_state != QueryState.END:
                print(f"📍 当前状态: {self.current_state.value}")
                
                if self.current_state == QueryState.INITIAL:
                    self._transition_to(QueryState.ENTITY_RECOGNITION)
                    
                elif self.current_state == QueryState.ENTITY_RECOGNITION:
                    if self._recognize_entities():
                        self._transition_to(QueryState.INTENT_ANALYSIS)
                    else:
                        self._transition_to(QueryState.ERROR_HANDLING)
                        
                elif self.current_state == QueryState.INTENT_ANALYSIS:
                    if self._analyze_intent():
                        self._transition_to(QueryState.QUERY_GENERATION)
                    else:
                        self._transition_to(QueryState.ERROR_HANDLING)
                        
                elif self.current_state == QueryState.QUERY_GENERATION:
                    if self._generate_query():
                        self._transition_to(QueryState.RESULT_PROCESSING)
                    else:
                        self._transition_to(QueryState.ERROR_HANDLING)
                        
                elif self.current_state == QueryState.RESULT_PROCESSING:
                    if self._process_results():
                        self._transition_to(QueryState.RESPONSE_GENERATION)
                    else:
                        self._transition_to(QueryState.ERROR_HANDLING)
                        
                elif self.current_state == QueryState.RESPONSE_GENERATION:
                    response = self._generate_response()
                    self._transition_to(QueryState.END)
                    return response
                    
                elif self.current_state == QueryState.ERROR_HANDLING:
                    response = self._handle_error()
                    self._transition_to(QueryState.END)
                    return response
                    
        except Exception as e:
            print(f"❌ 状态机执行错误: {e}")
            return "抱歉，系统出现错误，请稍后重试。"
    
    def _transition_to(self, new_state: QueryState):
        """状态转移方法"""
        if new_state in self.state_transitions[self.current_state]:
            print(f"🔄 状态转移: {self.current_state.value} → {new_state.value}")
            self.current_state = new_state
        else:
            raise ValueError(f"非法状态转移: {self.current_state} → {new_state}")
    
    def _recognize_entities(self) -> bool:
        """S1: 实体识别状态处理"""
        print("🔍 执行实体识别...")
        user_input = self.context['user_input']
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, user_input)
                if matches:
                    self.entities[entity_type] = matches
                    print(f"  识别到{entity_type}: {matches}")
        
        print(f"✅ 实体识别完成，共识别到 {len(self.entities)} 类实体")
        return len(self.entities) > 0
    
    def _analyze_intent(self) -> bool:
        """S2: 意图分析状态处理"""
        print("🎯 执行意图分析...")
        user_input = self.context['user_input']
        
        intent_scores = {}
        for intent, keywords in self.intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in user_input)
            if score > 0:
                intent_scores[intent] = score
        
        if intent_scores:
            self.intent = max(intent_scores, key=intent_scores.get)
            print(f"✅ 识别意图: {self.intent.value}")
            return True
        else:
            self.intent = QueryIntent.UNKNOWN
            print("⚠️ 未能识别明确意图")
            return False
    
    def _generate_query(self) -> bool:
        """S3: 查询生成状态处理"""
        print("📝 生成Neo4j查询...")
        
        try:
            if self.intent == QueryIntent.INVESTOR_INFO:
                self.context['cypher_query'] = self._build_investor_query()
            elif self.intent == QueryIntent.COMPANY_INFO:
                self.context['cypher_query'] = self._build_company_query()
            elif self.intent == QueryIntent.INVESTMENT_RELATION:
                self.context['cypher_query'] = self._build_investment_query()
            elif self.intent == QueryIntent.STATISTICAL_ANALYSIS:
                self.context['cypher_query'] = self._build_statistical_query()
            else:
                return False
            
            print(f"✅ 查询生成成功")
            return True
        except Exception as e:
            print(f"❌ 查询生成失败: {e}")
            return False
    
    def _build_investor_query(self) -> str:
        """构建投资机构查询"""
        if 'investor' in self.entities:
            investor_name = self.entities['investor'][0]
            return f"""
            MATCH (investor)
            WHERE investor.name CONTAINS '{investor_name}' 
            AND (investor:AngelInvestor OR investor:VentureCapital)
            OPTIONAL MATCH (investor)-[:LOCATED_IN]->(location:Location)
            OPTIONAL MATCH (investor)-[:BELONGS_TO_INDUSTRY]->(industry:Industry)
            RETURN investor.name as 机构名称, 
                   investor.description as 描述,
                   location.name as 地区,
                   industry.name as 行业
            LIMIT 5
            """
        else:
            return """
            MATCH (investor)
            WHERE investor:AngelInvestor OR investor:VentureCapital
            RETURN investor.name as 机构名称, investor.description as 描述
            LIMIT 10
            """
    
    def _build_company_query(self) -> str:
        """构建公司查询"""
        if 'company' in self.entities:
            company_name = self.entities['company'][0]
            return f"""
            MATCH (company:StartupCompany)
            WHERE company.name CONTAINS '{company_name}'
            OPTIONAL MATCH (company)-[:LOCATED_IN]->(location:Location)
            RETURN company.name as 公司名称,
                   company.description as 描述,
                   location.name as 地区
            LIMIT 5
            """
        else:
            return """
            MATCH (company:StartupCompany)
            RETURN company.name as 公司名称, company.description as 描述
            LIMIT 10
            """
    
    def _build_investment_query(self) -> str:
        """构建投资关系查询"""
        if 'investor' in self.entities and 'company' in self.entities:
            investor_name = self.entities['investor'][0]
            company_name = self.entities['company'][0]
            return f"""
            MATCH (investor)-[r:INVESTS]->(company:StartupCompany)
            WHERE investor.name CONTAINS '{investor_name}' 
            AND company.name CONTAINS '{company_name}'
            RETURN investor.name as 投资机构,
                   company.name as 被投公司,
                   r.roundType as 投资轮次,
                   r.investmentDate as 投资日期
            """
        elif 'investor' in self.entities:
            investor_name = self.entities['investor'][0]
            return f"""
            MATCH (investor)-[r:INVESTS]->(company:StartupCompany)
            WHERE investor.name CONTAINS '{investor_name}'
            RETURN investor.name as 投资机构,
                   company.name as 被投公司,
                   r.roundType as 投资轮次,
                   r.investmentDate as 投资日期
            LIMIT 10
            """
        else:
            return """
            MATCH (investor)-[r:INVESTS]->(company:StartupCompany)
            RETURN investor.name as 投资机构,
                   company.name as 被投公司,
                   r.roundType as 投资轮次,
                   r.investmentDate as 投资日期
            LIMIT 10
            """
    
    def _build_statistical_query(self) -> str:
        """构建统计分析查询"""
        return """
        MATCH (investor)-[r:INVESTS]->(company)
        RETURN investor.name as 投资机构, count(r) as 投资次数
        ORDER BY 投资次数 DESC
        LIMIT 10
        """
    
    def _process_results(self) -> bool:
        """S4: 结果处理状态处理"""
        print("📊 处理查询结果...")
        
        try:
            cypher_query = self.context['cypher_query']
            self.query_result = self.neo4j.execute_query(cypher_query)
            print(f"✅ 查询成功，返回 {len(self.query_result)} 条结果")
            return True
        except Exception as e:
            print(f"❌ 查询执行失败: {e}")
            return False
    
    def _generate_response(self) -> str:
        """S5: 响应生成状态处理"""
        print("💬 生成自然语言回答...")
        
        if not self.query_result:
            return "抱歉，没有找到相关信息。"
        
        if self.intent == QueryIntent.INVESTOR_INFO:
            return self._format_investor_response()
        elif self.intent == QueryIntent.COMPANY_INFO:
            return self._format_company_response()
        elif self.intent == QueryIntent.INVESTMENT_RELATION:
            return self._format_investment_response()
        elif self.intent == QueryIntent.STATISTICAL_ANALYSIS:
            return self._format_statistical_response()
        else:
            return self._format_general_response()
    
    def _format_investor_response(self) -> str:
        """格式化投资机构回答"""
        response = "📈 投资机构信息如下：\n\n"
        for i, record in enumerate(self.query_result[:5], 1):
            response += f"{i}. **{record.get('机构名称', 'N/A')}**\n"
            if record.get('描述'):
                response += f"   描述: {record['描述']}\n"
            if record.get('地区'):
                response += f"   地区: {record['地区']}\n"
            if record.get('行业'):
                response += f"   行业: {record['行业']}\n"
            response += "\n"
        return response
    
    def _format_company_response(self) -> str:
        """格式化公司信息回答"""
        response = "🏢 公司信息如下：\n\n"
        for i, record in enumerate(self.query_result[:5], 1):
            response += f"{i}. **{record.get('公司名称', 'N/A')}**\n"
            if record.get('描述'):
                response += f"   描述: {record['描述']}\n"
            if record.get('地区'):
                response += f"   地区: {record['地区']}\n"
            response += "\n"
        return response
    
    def _format_investment_response(self) -> str:
        """格式化投资关系回答"""
        response = "💰 投资关系如下：\n\n"
        for i, record in enumerate(self.query_result[:10], 1):
            response += f"{i}. **{record.get('投资机构', 'N/A')}** 投资了 **{record.get('被投公司', 'N/A')}**\n"
            if record.get('投资轮次'):
                response += f"   轮次: {record['投资轮次']}\n"
            if record.get('投资日期'):
                response += f"   日期: {record['投资日期']}\n"
            response += "\n"
        return response
    
    def _format_statistical_response(self) -> str:
        """格式化统计分析回答"""
        response = "📊 统计分析结果：\n\n"
        for i, record in enumerate(self.query_result[:10], 1):
            response += f"{i}. **{record.get('投资机构', 'N/A')}**: {record.get('投资次数', 0)} 次投资\n"
        return response
    
    def _format_general_response(self) -> str:
        """格式化通用回答"""
        response = "📋 查询结果：\n\n"
        for i, record in enumerate(self.query_result[:5], 1):
            response += f"{i}. {dict(record)}\n"
        return response
    
    def _handle_error(self) -> str:
        """S6: 错误处理状态处理"""
        print("⚠️ 处理错误情况...")
        return "抱歉，我无法理解您的问题。请尝试询问关于投资机构、公司或投资关系的问题。"
    
    def reset_context(self):
        """重置查询上下文"""
        self.current_state = QueryState.INITIAL
        self.context = {}
        self.entities = {}
        self.intent = None
        self.query_result = None

def main():
    """测试状态机"""
    # 连接Neo4j
    neo4j = Neo4jConnector(
        uri="bolt://localhost:7687",
        user="neo4j",
        password="angelinvestment"
    )
    
    if not neo4j.graph:
        print("❌ 无法连接到Neo4j数据库")
        return
    
    # 创建状态机
    qa_system = AngelInvestmentQAStateMachine(neo4j)
    
    # 测试查询
    test_queries = [
        "华控基金的信息",
        "水木源华公司的详细信息",
        "华控基金投资了哪些公司？",
        "最活跃的投资机构有哪些？"
    ]
    
    print("🚀 天使投资智能问答系统测试")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\n👤 用户问题: {query}")
        response = qa_system.process_query(query)
        print(f"🤖 系统回答:\n{response}")
        print("-" * 30)

if __name__ == "__main__":
    main()
