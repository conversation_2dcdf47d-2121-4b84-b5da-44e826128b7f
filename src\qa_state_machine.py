#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import json
from enum import Enum, auto
from typing import Dict, List, Tuple, Any, Optional, Callable

class State(Enum):
    """状态枚举类，定义了状态机的所有可能状态"""
    INITIAL = auto()                # 初始状态
    IDENTIFY_INVESTOR = auto()      # 识别投资机构
    CONFIRM_INVESTOR = auto()       # 确认投资机构
    IDENTIFY_COMPANY = auto()       # 识别创业公司
    CONFIRM_COMPANY = auto()        # 确认创业公司
    IDENTIFY_INTENT = auto()        # 识别查询意图
    GENERATE_QUERY = auto()         # 生成查询
    EXECUTE_QUERY = auto()          # 执行查询
    FORMAT_RESULT = auto()          # 格式化结果
    RETURN_RESULT = auto()          # 返回结果
    END = auto()                    # 结束状态

class Intent(Enum):
    """意图枚举类，定义了系统能够识别的查询意图"""
    # 投资机构相关意图
    INVESTOR_PORTFOLIO = auto()     # 投资机构的投资组合
    INVESTOR_ROUNDS = auto()        # 投资机构的投资轮次分布
    INVESTOR_FIELDS = auto()        # 投资机构的投资领域
    INVESTOR_SPECIFIC_ROUND = auto() # 投资机构的特定轮次投资
    INVESTOR_CO_INVESTMENT = auto() # 投资机构的共同投资

    # 创业公司相关意图
    COMPANY_FUNDING_HISTORY = auto() # 创业公司的融资历史
    COMPANY_SPECIFIC_ROUND = auto()  # 创业公司的特定轮次融资
    COMPANY_TOTAL_FUNDING = auto()   # 创业公司的融资总额
    COMPANY_EARLY_INVESTORS = auto() # 创业公司的早期投资方
    COMPANY_COMMON_INVESTORS = auto() # 创业公司的共同投资方

    # 其他意图
    UNKNOWN = auto()                 # 未知意图

class EntityType(Enum):
    """实体类型枚举类，定义了系统能够识别的实体类型"""
    INVESTOR = auto()                # 投资机构
    COMPANY = auto()                 # 创业公司
    ROUND = auto()                   # 投资轮次
    FIELD = auto()                   # 投资领域
    UNKNOWN = auto()                 # 未知实体

class QAStateMachine:
    """问答系统状态机类，实现了基于有限状态自动机的问答系统"""

    def __init__(self, investor_data: List[str], company_data: List[str], round_data: List[str], field_data: List[str]):
        """
        初始化状态机

        Args:
            investor_data: 投资机构数据列表
            company_data: 创业公司数据列表
            round_data: 投资轮次数据列表
            field_data: 投资领域数据列表
        """
        self.state = State.INITIAL
        self.investor_data = investor_data
        self.company_data = company_data
        self.round_data = round_data
        self.field_data = field_data

        # 当前会话的上下文
        self.context = {
            'entity_type': None,     # 实体类型
            'entity_name': None,     # 实体名称
            'intent': None,          # 查询意图
            'parameters': {},        # 查询参数
            'query': None,           # 生成的查询
            'result': None           # 查询结果
        }

        # 状态转移函数映射
        self.state_handlers = {
            State.INITIAL: self._handle_initial,
            State.IDENTIFY_INVESTOR: self._handle_identify_investor,
            State.CONFIRM_INVESTOR: self._handle_confirm_investor,
            State.IDENTIFY_COMPANY: self._handle_identify_company,
            State.CONFIRM_COMPANY: self._handle_confirm_company,
            State.IDENTIFY_INTENT: self._handle_identify_intent,
            State.GENERATE_QUERY: self._handle_generate_query,
            State.EXECUTE_QUERY: self._handle_execute_query,
            State.FORMAT_RESULT: self._handle_format_result,
            State.RETURN_RESULT: self._handle_return_result,
            State.END: self._handle_end
        }

        # 意图识别模式
        self.intent_patterns = {
            # 投资机构相关意图
            Intent.INVESTOR_PORTFOLIO: [
                r'(.*)(投资|投了)(哪些|什么)(公司|企业|项目)(.*)',
                r'(.*)的(投资组合|投资公司|投资项目)(.*)',
                r'(.*)都(投资|投了)(哪些|什么)(公司|企业|项目)(.*)',
                r'(.*)投资了哪些公司(.*)'
            ],
            Intent.INVESTOR_ROUNDS: [
                r'(.*)在(哪些|什么)(轮次|阶段)(投资|投的)(最多|最活跃|最频繁)(.*)',
                r'(.*)的(投资轮次|投资阶段)(分布|情况)(.*)',
                r'(.*)(主要|偏好|喜欢)(投资|投)(哪些|什么)(轮次|阶段)(.*)'
            ],
            Intent.INVESTOR_FIELDS: [
                r'(.*)(主要|偏好|喜欢)(投资|投)(哪些|什么)(领域|行业|方向)(.*)',
                r'(.*)的(投资领域|投资行业|投资方向)(.*)',
                r'(.*)(关注|专注于)(哪些|什么)(领域|行业|方向)(.*)'
            ],
            Intent.INVESTOR_SPECIFIC_ROUND: [
                r'(.*)的(.*轮|.*阶段)(投资|投资组合|投资公司)(.*)',
                r'(.*)(在|投资)(.*轮|.*阶段)(的)(公司|企业|项目)(.*)',
                r'(.*)(投资|投了)(多少|几个|哪些)(.*轮|.*阶段)(的)(公司|企业|项目)(.*)'
            ],
            Intent.INVESTOR_CO_INVESTMENT: [
                r'(.*)和(.*)(有哪些|有什么|哪些)(共同|一起)(投资|投的)(公司|企业|项目)(.*)',
                r'(.*)和(.*)(共同|一起)(投资|投了)(哪些|什么)(公司|企业|项目)(.*)',
                r'(.*)和(.*)的(共同投资|共同投资组合|共同投资公司)(.*)'
            ],

            # 创业公司相关意图
            Intent.COMPANY_FUNDING_HISTORY: [
                r'(.*)的(融资历史|融资历程|融资过程)(.*)',
                r'(.*)(获得|拿到|经历)(了)(哪些|什么)(融资|投资)(.*)',
                r'(.*)(融资|融到|拿到)(了)(多少|哪些|什么)(轮)(投资|融资)(.*)'
            ],
            Intent.COMPANY_SPECIFIC_ROUND: [
                r'(.*)的(.*轮|.*阶段)(融资|投资)(是|的)(投资方|投资人|投资机构)(是|有)(谁|哪些)(.*)',
                r'(谁|哪些|什么)(机构|公司|投资方)(投资|投了)(.*)的(.*轮|.*阶段)(.*)',
                r'(.*)在(.*轮|.*阶段)(获得|拿到)(了)(谁|哪些|什么)(机构|公司|投资方)的(投资|融资)(.*)',
                r'(.*)(.{1,2}轮)融资的投资方是谁(.*)'
            ],
            Intent.COMPANY_TOTAL_FUNDING: [
                r'(.*)从(.*轮)到(.*轮)(融资|融到|拿到)(了)(多少|多大|多大规模)(的)(投资|融资)(.*)',
                r'(.*)的(总融资|总投资|融资总额|投资总额)(是|有)(多少|多大|多大规模)(.*)',
                r'(.*)(总共|一共|总计)(融资|融到|拿到)(了)(多少|多大|多大规模)(的)(投资|融资)(.*)'
            ],
            Intent.COMPANY_EARLY_INVESTORS: [
                r'(.*)的(最早|第一个|早期)(投资方|投资人|投资机构)(是|有)(谁|哪些)(.*)',
                r'(谁|哪些|什么)(机构|公司|投资方)(最早|第一个)(投资|投了)(.*)(.*)',
                r'(.*)的(天使轮|种子轮)(投资方|投资人|投资机构)(是|有)(谁|哪些)(.*)',
                r'(.*)最早的投资方是谁(.*)'
            ],
            Intent.COMPANY_COMMON_INVESTORS: [
                r'(.*)和(.*)(有哪些|有什么|哪些)(共同|相同)的(投资方|投资人|投资机构)(.*)',
                r'(哪些|什么)(投资方|投资人|投资机构)(同时|既)(投资|投了)(.*)和(.*)(.*)',
                r'(.*)和(.*)的(共同投资方|共同投资人|共同投资机构)(.*)'
            ]
        }

    def process(self, user_input: str) -> str:
        """
        处理用户输入，推进状态机

        Args:
            user_input: 用户输入的文本

        Returns:
            系统的响应文本
        """
        # 如果是新会话，重置状态
        if self.state == State.END:
            self.reset()

        # 保存用户输入到上下文
        self.context['user_input'] = user_input

        # 循环处理状态转移，直到需要返回结果给用户
        response = None
        while response is None:
            # 获取当前状态的处理函数
            handler = self.state_handlers.get(self.state)
            if handler:
                # 调用处理函数，获取下一个状态和响应
                next_state, response = handler()
                # 更新状态
                self.state = next_state
            else:
                # 未知状态，返回错误信息
                response = "系统出现错误，请重新开始。"
                self.state = State.END

        return response

    def reset(self):
        """重置状态机到初始状态"""
        self.state = State.INITIAL
        self.context = {
            'entity_type': None,
            'entity_name': None,
            'intent': None,
            'parameters': {},
            'query': None,
            'result': None
        }

    def _handle_initial(self) -> Tuple[State, Optional[str]]:
        """处理初始状态"""
        user_input = self.context.get('user_input', '')

        # 尝试识别投资机构
        for investor in self.investor_data:
            if investor in user_input:
                self.context['entity_type'] = EntityType.INVESTOR
                self.context['entity_name'] = investor
                return State.IDENTIFY_INVESTOR, None

        # 尝试识别创业公司
        for company in self.company_data:
            if company in user_input:
                self.context['entity_type'] = EntityType.COMPANY
                self.context['entity_name'] = company
                return State.IDENTIFY_COMPANY, None

        # 无法识别实体，询问用户
        return State.END, "抱歉，我无法识别您提到的投资机构或创业公司。请提供更明确的信息。"

    def _handle_identify_investor(self) -> Tuple[State, Optional[str]]:
        """处理识别投资机构状态"""
        # 已经识别到投资机构，直接确认
        return State.CONFIRM_INVESTOR, None

    def _handle_confirm_investor(self) -> Tuple[State, Optional[str]]:
        """处理确认投资机构状态"""
        # 投资机构已确认，识别查询意图
        return State.IDENTIFY_INTENT, None

    def _handle_identify_company(self) -> Tuple[State, Optional[str]]:
        """处理识别创业公司状态"""
        # 已经识别到创业公司，直接确认
        return State.CONFIRM_COMPANY, None

    def _handle_confirm_company(self) -> Tuple[State, Optional[str]]:
        """处理确认创业公司状态"""
        # 创业公司已确认，识别查询意图
        return State.IDENTIFY_INTENT, None

    def _handle_identify_intent(self) -> Tuple[State, Optional[str]]:
        """处理识别查询意图状态"""
        user_input = self.context.get('user_input', '')
        entity_type = self.context.get('entity_type')

        # 根据实体类型选择相关的意图模式
        relevant_intents = []
        if entity_type == EntityType.INVESTOR:
            relevant_intents = [
                Intent.INVESTOR_PORTFOLIO,
                Intent.INVESTOR_ROUNDS,
                Intent.INVESTOR_FIELDS,
                Intent.INVESTOR_SPECIFIC_ROUND,
                Intent.INVESTOR_CO_INVESTMENT
            ]
        elif entity_type == EntityType.COMPANY:
            relevant_intents = [
                Intent.COMPANY_FUNDING_HISTORY,
                Intent.COMPANY_SPECIFIC_ROUND,
                Intent.COMPANY_TOTAL_FUNDING,
                Intent.COMPANY_EARLY_INVESTORS,
                Intent.COMPANY_COMMON_INVESTORS
            ]

        # 尝试匹配意图
        for intent in relevant_intents:
            patterns = self.intent_patterns.get(intent, [])
            for pattern in patterns:
                match = re.search(pattern, user_input)
                if match:
                    self.context['intent'] = intent

                    # 提取额外参数
                    if intent == Intent.INVESTOR_SPECIFIC_ROUND:
                        # 提取轮次信息
                        for round_name in self.round_data:
                            if round_name in user_input:
                                self.context['parameters']['round'] = round_name
                                break
                    elif intent == Intent.INVESTOR_CO_INVESTMENT:
                        # 提取第二个投资机构
                        for investor in self.investor_data:
                            if investor != self.context['entity_name'] and investor in user_input:
                                self.context['parameters']['co_investor'] = investor
                                break
                    elif intent == Intent.COMPANY_SPECIFIC_ROUND:
                        # 提取轮次信息
                        for round_name in self.round_data:
                            if round_name in user_input:
                                self.context['parameters']['round'] = round_name
                                break
                    elif intent == Intent.COMPANY_TOTAL_FUNDING:
                        # 提取起始轮次和结束轮次
                        rounds_in_query = []
                        for round_name in self.round_data:
                            if round_name in user_input:
                                rounds_in_query.append(round_name)
                        if len(rounds_in_query) >= 2:
                            self.context['parameters']['start_round'] = rounds_in_query[0]
                            self.context['parameters']['end_round'] = rounds_in_query[1]
                    elif intent == Intent.COMPANY_COMMON_INVESTORS:
                        # 提取第二个公司
                        for company in self.company_data:
                            if company != self.context['entity_name'] and company in user_input:
                                self.context['parameters']['co_company'] = company
                                break

                    return State.GENERATE_QUERY, None

        # 无法识别意图，询问用户
        return State.END, "抱歉，我无法理解您的查询意图。请尝试用不同的方式提问。"

    def _handle_generate_query(self) -> Tuple[State, Optional[str]]:
        """处理生成查询状态"""
        # 根据实体类型和查询意图生成查询
        entity_type = self.context.get('entity_type')
        entity_name = self.context.get('entity_name')
        intent = self.context.get('intent')
        parameters = self.context.get('parameters', {})

        # 生成查询（这里只是示例，实际查询会在连接数据库的代码中实现）
        query = {
            'entity_type': entity_type,
            'entity_name': entity_name,
            'intent': intent,
            'parameters': parameters
        }

        self.context['query'] = query
        return State.EXECUTE_QUERY, None

    def _handle_execute_query(self) -> Tuple[State, Optional[str]]:
        """处理执行查询状态"""
        # 这里应该执行实际的数据库查询
        # 在本示例中，我们只是模拟查询结果
        query = self.context.get('query', {})

        # 模拟查询结果
        result = {
            'status': 'success',
            'data': []
        }

        self.context['result'] = result
        return State.FORMAT_RESULT, None

    def _handle_format_result(self) -> Tuple[State, Optional[str]]:
        """处理格式化结果状态"""
        # 格式化查询结果
        result = self.context.get('result', {})

        # 这里应该根据查询结果生成用户友好的响应
        # 在本示例中，我们只是返回一个简单的响应
        formatted_result = "查询结果将在这里显示。"

        self.context['formatted_result'] = formatted_result
        return State.RETURN_RESULT, None

    def _handle_return_result(self) -> Tuple[State, Optional[str]]:
        """处理返回结果状态"""
        # 返回格式化的结果
        formatted_result = self.context.get('formatted_result', "抱歉，没有找到相关结果。")
        return State.END, formatted_result

    def _handle_end(self) -> Tuple[State, Optional[str]]:
        """处理结束状态"""
        # 重置状态机
        self.reset()
        return State.INITIAL, "请输入新的查询。"
