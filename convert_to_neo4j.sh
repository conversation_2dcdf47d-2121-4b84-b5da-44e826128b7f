#!/bin/bash

echo "中国天使投资知识图谱 - 数据转换工具"
echo "=============================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python环境，请安装Python 3.6+"
    exit 1
fi

# 创建必要的目录
mkdir -p neo4j_import

echo
echo "开始转换数据..."
python3 src/convert_to_neo4j_csv.py

if [ $? -ne 0 ]; then
    echo "转换过程中出现错误，请检查日志。"
    exit 1
fi

echo
echo "数据转换完成！"
echo "CSV文件已保存到 neo4j_import 目录。"
echo
echo "请使用以下命令将数据导入到Neo4j："
echo
echo "neo4j-admin import --database=angelinvestment \\"
echo "    --nodes=AngelInvestor=neo4j_import/angel_investor.csv \\"
echo "    --nodes=VentureCapital=neo4j_import/venture_capital.csv \\"
echo "    --nodes=StartupCompany=neo4j_import/company.csv \\"
echo "    --nodes=InvestmentRound=neo4j_import/investment_round.csv \\"
echo "    --nodes=InvestmentEvent=neo4j_import/investment_event.csv \\"
echo "    --relationships=INVESTS=neo4j_import/invests_relationship.csv \\"
echo "    --relationships=HAS_INVESTMENT_EVENT=neo4j_import/has_event_relationship.csv \\"
echo "    --relationships=HAS_ROUND=neo4j_import/has_round_relationship.csv \\"
echo "    --delimiter=\",\" --array-delimiter=\";\" --id-type=STRING"
echo

read -p "按任意键继续..." key
