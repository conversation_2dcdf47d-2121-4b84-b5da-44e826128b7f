# 中国天使投资知识图谱 - Neo4j数据导入指南

本文档详细说明了如何使用Python脚本将中国天使投资数据导入到Neo4j图数据库中。

## 1. 概述

数据导入过程包括以下几个步骤：

1. **数据转换**：将JSON数据转换为Neo4j可导入的CSV格式。
2. **Neo4j导入**：使用Neo4j批量导入工具将CSV数据导入到Neo4j数据库中。
3. **配置更新**：更新Neo4j配置，设置默认数据库。
4. **服务重启**：重启Neo4j服务，使配置生效。

## 2. 准备工作

### 2.1 环境要求

- Python 3.6+
- Neo4j 4.0+
- 操作系统：Windows、Linux或macOS

### 2.2 安装依赖

本脚本不需要额外的Python依赖，只使用了标准库。

### 2.3 Neo4j设置

确保Neo4j已安装并且可以通过命令行访问。如果Neo4j不在系统PATH中，您需要在运行脚本时指定Neo4j安装路径。

## 3. 使用方法

### 3.1 使用批处理脚本（推荐）

我们提供了批处理脚本，方便用户选择不同的导入选项：

#### Windows

双击运行`run_neo4j_import.bat`，然后按照提示选择导入选项：

```
中国天使投资知识图谱 - Neo4j数据导入工具
======================================

请选择导入选项:
1. 完整导入流程（转换数据、导入Neo4j、更新配置、重启服务）
2. 仅转换数据和导入Neo4j（不更新配置和重启服务）
3. 仅导入Neo4j（假设CSV文件已存在）
4. 自定义Neo4j安装路径
5. 退出
```

#### Linux/macOS

在终端中运行：

```bash
chmod +x run_neo4j_import.sh  # 添加执行权限
./run_neo4j_import.sh
```

然后按照提示选择导入选项。

### 3.2 直接使用Python脚本

您也可以直接使用Python脚本，这样可以更灵活地控制导入过程：

```bash
# 完整导入流程
python src/import_to_neo4j.py

# 指定数据库名称
python src/import_to_neo4j.py --database=mydb

# 指定Neo4j安装路径
python src/import_to_neo4j.py --neo4j-home="C:/Program Files/Neo4j"

# 跳过某些步骤
python src/import_to_neo4j.py --skip-convert --skip-config --skip-restart
```

### 3.3 命令行参数

Python脚本支持以下命令行参数：

- `--database`：指定数据库名称，默认为`angelinvestment`。
- `--neo4j-home`：指定Neo4j安装路径，如果Neo4j在系统PATH中，可以不指定。
- `--skip-convert`：跳过数据转换步骤，假设CSV文件已存在。
- `--skip-config`：跳过配置更新步骤。
- `--skip-restart`：跳过重启Neo4j服务步骤。

## 4. 导入过程详解

### 4.1 数据转换

数据转换步骤会运行`src/convert_to_neo4j_csv.py`脚本，将JSON数据转换为Neo4j可导入的CSV格式。转换后的CSV文件将保存在`neo4j_import`目录中。

生成的CSV文件包括：

- `angel_investor.csv`：天使投资机构节点
- `venture_capital.csv`：风险投资机构节点
- `company.csv`：创业公司节点
- `investment_round.csv`：投资轮次节点
- `investment_event.csv`：投资事件节点
- `invests_relationship.csv`：投资关系
- `has_event_relationship.csv`：投资事件关系
- `has_round_relationship.csv`：投资轮次关系

### 4.2 Neo4j导入

Neo4j导入步骤会使用Neo4j批量导入工具将CSV数据导入到Neo4j数据库中。导入命令类似于：

```bash
neo4j-admin import --database=angelinvestment \
    --nodes=AngelInvestor=neo4j_import/angel_investor.csv \
    --nodes=VentureCapital=neo4j_import/venture_capital.csv \
    --nodes=StartupCompany=neo4j_import/company.csv \
    --nodes=InvestmentRound=neo4j_import/investment_round.csv \
    --nodes=InvestmentEvent=neo4j_import/investment_event.csv \
    --relationships=INVESTS=neo4j_import/invests_relationship.csv \
    --relationships=HAS_INVESTMENT_EVENT=neo4j_import/has_event_relationship.csv \
    --relationships=HAS_ROUND=neo4j_import/has_round_relationship.csv \
    --delimiter="," --array-delimiter=";" --id-type=STRING
```

### 4.3 配置更新

配置更新步骤会修改Neo4j配置文件，设置默认数据库为导入的数据库。这样，当Neo4j启动时，会自动使用这个数据库。

配置文件通常位于：

- Windows：`C:/Program Files/Neo4j/conf/neo4j.conf`
- Linux：`/etc/neo4j/neo4j.conf`
- macOS：`/usr/local/Cellar/neo4j/*/libexec/conf/neo4j.conf`

更新的配置项为：

```
dbms.default_database=angelinvestment
```

### 4.4 服务重启

服务重启步骤会停止并重新启动Neo4j服务，使配置更改生效。

## 5. 故障排除

### 5.1 常见问题

#### 无法找到Neo4j命令

如果脚本无法找到`neo4j-admin`命令，请使用`--neo4j-home`参数指定Neo4j安装路径：

```bash
python src/import_to_neo4j.py --neo4j-home="C:/Program Files/Neo4j"
```

#### 导入失败

如果导入过程失败，请检查日志文件`neo4j_import.log`，查看详细的错误信息。常见的导入失败原因包括：

- CSV文件格式错误
- 数据类型不匹配
- 文件路径错误
- 权限问题

#### 无法更新配置文件

如果脚本无法更新Neo4j配置文件，您可能需要手动更新配置。打开Neo4j配置文件，添加或修改以下行：

```
dbms.default_database=angelinvestment
```

### 5.2 日志文件

脚本会生成一个日志文件`neo4j_import.log`，记录导入过程中的详细信息。如果遇到问题，请查看这个日志文件。

## 6. 导入后的操作

### 6.1 验证导入结果

导入完成后，您可以使用Neo4j Browser验证导入结果：

1. 打开浏览器，访问`http://localhost:7474`
2. 使用Neo4j用户名和密码登录
3. 运行以下Cypher查询，检查导入的数据：

```cypher
// 查看节点数量
MATCH (n) RETURN labels(n) AS NodeType, count(*) AS Count;

// 查看关系数量
MATCH ()-[r]->() RETURN type(r) AS RelationType, count(*) AS Count;

// 查看天使投资机构
MATCH (a:AngelInvestor) RETURN a.name LIMIT 10;

// 查看投资关系
MATCH (i)-[r:INVESTS]->(c) RETURN i.name, c.name LIMIT 10;
```

### 6.2 数据分析

导入完成后，您可以使用Cypher查询语言对数据进行分析。例如：

```cypher
// 查询投资最多的机构
MATCH (i)-[r:INVESTS]->()
RETURN i.name, count(r) AS investment_count
ORDER BY investment_count DESC
LIMIT 10;

// 查询获得投资最多的公司
MATCH ()-[r:INVESTS]->(c)
RETURN c.name, count(r) AS investor_count
ORDER BY investor_count DESC
LIMIT 10;

// 查询各轮次的投资事件数量
MATCH (e:InvestmentEvent)-[:HAS_ROUND]->(r)
RETURN r.name, count(e) AS event_count
ORDER BY event_count DESC;
```

## 7. 结论

通过本指南，您应该能够成功地将中国天使投资数据导入到Neo4j图数据库中。导入后的数据可以用于各种分析和可视化，帮助您深入了解中国天使投资生态系统。

如果您在导入过程中遇到任何问题，请查看日志文件或联系技术支持。
