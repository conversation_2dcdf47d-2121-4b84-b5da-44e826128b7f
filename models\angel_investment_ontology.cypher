// 创建约束和索引
CREATE CONSTRAINT IF NOT EXISTS FOR (n:InvestmentInstitution) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:AngelInvestor) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:VentureCapital) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Person) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Investor) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Founder) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Company) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:StartupCompany) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:InvestmentEvent) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:InvestmentRound) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:AngelRound) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:RoundA) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:RoundB) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:InvestmentField) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Technology) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Healthcare) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Education) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Location) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:City) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Province) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT IF NOT EXISTS FOR (n:Country) REQUIRE n.id IS UNIQUE;

// 创建类节点
CREATE (:InvestmentInstitution {id: 'ai:InvestmentInstitution', name: '投资机构', description: '天使投资公司、风险投资机构等'});
CREATE (:AngelInvestor {id: 'ai:AngelInvestor', name: '天使投资机构', description: '专注于早期投资的天使投资机构'});
CREATE (:VentureCapital {id: 'ai:VentureCapital', name: '风险投资机构', description: '专注于风险投资的机构'});
CREATE (:Person {id: 'ai:Person', name: '人物', description: '与投资相关的人物，如投资人、创始人等'});
CREATE (:Investor {id: 'ai:Investor', name: '投资人', description: '个人投资者'});
CREATE (:Founder {id: 'ai:Founder', name: '创始人', description: '企业或投资机构的创始人'});
CREATE (:Company {id: 'ai:Company', name: '公司', description: '企业实体'});
CREATE (:StartupCompany {id: 'ai:StartupCompany', name: '创业公司', description: '初创企业'});
CREATE (:InvestmentEvent {id: 'ai:InvestmentEvent', name: '投资事件', description: '投资活动'});
CREATE (:InvestmentRound {id: 'ai:InvestmentRound', name: '投资轮次', description: '投资的轮次，如天使轮、A轮等'});
CREATE (:AngelRound {id: 'ai:AngelRound', name: '天使轮', description: '天使投资轮次'});
CREATE (:RoundA {id: 'ai:RoundA', name: 'A轮', description: 'A轮融资'});
CREATE (:RoundB {id: 'ai:RoundB', name: 'B轮', description: 'B轮融资'});
CREATE (:InvestmentField {id: 'ai:InvestmentField', name: '投资领域', description: '投资的行业领域'});
CREATE (:Technology {id: 'ai:Technology', name: '科技', description: '科技领域'});
CREATE (:Healthcare {id: 'ai:Healthcare', name: '医疗健康', description: '医疗健康领域'});
CREATE (:Education {id: 'ai:Education', name: '教育', description: '教育领域'});
CREATE (:Location {id: 'ai:Location', name: '地理位置', description: '地理位置信息'});
CREATE (:City {id: 'ai:City', name: '城市', description: '城市'});
CREATE (:Province {id: 'ai:Province', name: '省份', description: '省份'});
CREATE (:Country {id: 'ai:Country', name: '国家', description: '国家'});

// 创建类层次关系
MATCH (c:AngelInvestor {id: 'ai:AngelInvestor'}), (p:InvestmentInstitution {id: 'ai:InvestmentInstitution'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:VentureCapital {id: 'ai:VentureCapital'}), (p:InvestmentInstitution {id: 'ai:InvestmentInstitution'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:Investor {id: 'ai:Investor'}), (p:Person {id: 'ai:Person'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:Founder {id: 'ai:Founder'}), (p:Person {id: 'ai:Person'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:StartupCompany {id: 'ai:StartupCompany'}), (p:Company {id: 'ai:Company'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:AngelRound {id: 'ai:AngelRound'}), (p:InvestmentRound {id: 'ai:InvestmentRound'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:RoundA {id: 'ai:RoundA'}), (p:InvestmentRound {id: 'ai:InvestmentRound'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:RoundB {id: 'ai:RoundB'}), (p:InvestmentRound {id: 'ai:InvestmentRound'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:Technology {id: 'ai:Technology'}), (p:InvestmentField {id: 'ai:InvestmentField'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:Healthcare {id: 'ai:Healthcare'}), (p:InvestmentField {id: 'ai:InvestmentField'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:Education {id: 'ai:Education'}), (p:InvestmentField {id: 'ai:InvestmentField'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:City {id: 'ai:City'}), (p:Location {id: 'ai:Location'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:Province {id: 'ai:Province'}), (p:Location {id: 'ai:Location'}) CREATE (c)-[:IS_A]->(p);
MATCH (c:Country {id: 'ai:Country'}), (p:Location {id: 'ai:Location'}) CREATE (c)-[:IS_A]->(p);

// 创建对象属性关系模板
// 关系: InvestmentInstitution -[:hasFounder]-> Founder
// 示例: MATCH (a:InvestmentInstitution), (b:Founder) WHERE ... CREATE (a)-[:hasFounder {name: '有创始人', description: '机构或公司的创始人关系'}]->(b);
// 关系: Company -[:hasFounder]-> Founder
// 示例: MATCH (a:Company), (b:Founder) WHERE ... CREATE (a)-[:hasFounder {name: '有创始人', description: '机构或公司的创始人关系'}]->(b);
// 关系: InvestmentInstitution -[:invests]-> Company
// 示例: MATCH (a:InvestmentInstitution), (b:Company) WHERE ... CREATE (a)-[:invests {name: '投资', description: '投资关系'}]->(b);
// 关系: Investor -[:invests]-> Company
// 示例: MATCH (a:Investor), (b:Company) WHERE ... CREATE (a)-[:invests {name: '投资', description: '投资关系'}]->(b);
// 关系: InvestmentInstitution -[:hasInvestmentEvent]-> InvestmentEvent
// 示例: MATCH (a:InvestmentInstitution), (b:InvestmentEvent) WHERE ... CREATE (a)-[:hasInvestmentEvent {name: '有投资事件', description: '与投资事件的关联'}]->(b);
// 关系: Company -[:hasInvestmentEvent]-> InvestmentEvent
// 示例: MATCH (a:Company), (b:InvestmentEvent) WHERE ... CREATE (a)-[:hasInvestmentEvent {name: '有投资事件', description: '与投资事件的关联'}]->(b);
// 关系: InvestmentEvent -[:hasRound]-> InvestmentRound
// 示例: MATCH (a:InvestmentEvent), (b:InvestmentRound) WHERE ... CREATE (a)-[:hasRound {name: '有轮次', description: '投资事件的轮次'}]->(b);
// 关系: InvestmentInstitution -[:focusesOn]-> InvestmentField
// 示例: MATCH (a:InvestmentInstitution), (b:InvestmentField) WHERE ... CREATE (a)-[:focusesOn {name: '关注领域', description: '投资机构关注的领域'}]->(b);
// 关系: InvestmentInstitution -[:locatedIn]-> Location
// 示例: MATCH (a:InvestmentInstitution), (b:Location) WHERE ... CREATE (a)-[:locatedIn {name: '位于', description: '实体的地理位置'}]->(b);
// 关系: Company -[:locatedIn]-> Location
// 示例: MATCH (a:Company), (b:Location) WHERE ... CREATE (a)-[:locatedIn {name: '位于', description: '实体的地理位置'}]->(b);
// 关系: Investor -[:manages]-> InvestmentInstitution
// 示例: MATCH (a:Investor), (b:InvestmentInstitution) WHERE ... CREATE (a)-[:manages {name: '管理', description: '投资人管理的投资机构'}]->(b);

// 数据属性
// 属性: Thing.name
// 示例: MATCH (n:Thing) SET n.name = '值';
// 属性: InvestmentInstitution.foundingDate
// 示例: MATCH (n:InvestmentInstitution) SET n.foundingDate = '值';
// 属性: Company.foundingDate
// 示例: MATCH (n:Company) SET n.foundingDate = '值';
// 属性: InvestmentEvent.investmentAmount
// 示例: MATCH (n:InvestmentEvent) SET n.investmentAmount = '值';
// 属性: InvestmentEvent.investmentDate
// 示例: MATCH (n:InvestmentEvent) SET n.investmentDate = '值';
// 属性: Thing.description
// 示例: MATCH (n:Thing) SET n.description = '值';

// 示例数据

// 创建投资机构节点
CREATE (sequoia:AngelInvestor {id: 'ai:sequoia', name: '红杉资本中国', foundingDate: '2005-09-01', description: '红杉资本中国基金是全球知名风险投资机构红杉资本在中国设立的基金'});
CREATE (idg:VentureCapital {id: 'ai:idg', name: 'IDG资本', foundingDate: '1992-01-01', description: 'IDG资本是全球领先的投资机构，是最早进入中国市场的外资风险投资机构之一'});
CREATE (zhen:AngelInvestor {id: 'ai:zhen', name: '真格基金', foundingDate: '2011-01-01', description: '真格基金是中国知名的天使投资机构，专注于早期投资'});

// 创建投资人节点
CREATE (shen:Investor {id: 'ai:shen', name: '沈南鹏', position: '创始及执行合伙人', description: '红杉资本中国基金创始及执行合伙人'});
CREATE (xu:Investor {id: 'ai:xu', name: '徐小平', position: '创始人', description: '真格基金创始人'});
CREATE (xiong:Investor {id: 'ai:xiong', name: '熊晓鸽', position: '创始人', description: 'IDG资本创始人'});

// 创建创始人节点
CREATE (shen_founder:Founder {id: 'ai:shen_founder', name: '沈南鹏', description: '红杉资本中国基金创始人'});
CREATE (xu_founder:Founder {id: 'ai:xu_founder', name: '徐小平', description: '真格基金创始人'});
CREATE (xiong_founder:Founder {id: 'ai:xiong_founder', name: '熊晓鸽', description: 'IDG资本创始人'});

// 创建公司节点
CREATE (bytedance:StartupCompany {id: 'ai:bytedance', name: '字节跳动', foundingDate: '2012-03-01', description: '字节跳动是一家科技公司，旗下有抖音、今日头条等产品'});
CREATE (meituan:StartupCompany {id: 'ai:meituan', name: '美团', foundingDate: '2010-03-04', description: '美团是中国领先的生活服务电子商务平台'});
CREATE (xiaomi:StartupCompany {id: 'ai:xiaomi', name: '小米', foundingDate: '2010-04-06', description: '小米是一家以手机、智能硬件和IoT平台为核心的消费电子公司'});

// 创建投资领域节点
CREATE (tech:Technology {id: 'ai:tech', name: '科技', description: '科技领域'});
CREATE (health:Healthcare {id: 'ai:health', name: '医疗健康', description: '医疗健康领域'});
CREATE (edu:Education {id: 'ai:edu', name: '教育', description: '教育领域'});

// 创建地理位置节点
CREATE (beijing:City {id: 'ai:beijing', name: '北京', description: '中国首都'});
CREATE (shanghai:City {id: 'ai:shanghai', name: '上海', description: '中国经济中心'});

// 创建投资轮次节点
CREATE (angel:AngelRound {id: 'ai:angel', name: '天使轮', description: '天使投资轮次'});
CREATE (roundA:RoundA {id: 'ai:roundA', name: 'A轮', description: 'A轮融资'});
CREATE (roundB:RoundB {id: 'ai:roundB', name: 'B轮', description: 'B轮融资'});

// 创建投资事件节点
CREATE (event1:InvestmentEvent {id: 'ai:event1', name: '红杉资本投资字节跳动A轮', investmentAmount: 10000000, investmentDate: '2012-09-01', description: '红杉资本对字节跳动的A轮投资'});
CREATE (event2:InvestmentEvent {id: 'ai:event2', name: 'IDG资本投资小米天使轮', investmentAmount: 5000000, investmentDate: '2010-05-01', description: 'IDG资本对小米的天使轮投资'});
CREATE (event3:InvestmentEvent {id: 'ai:event3', name: '真格基金投资美团天使轮', investmentAmount: 3000000, investmentDate: '2010-04-01', description: '真格基金对美团的天使轮投资'});

// 创建关系
// 投资机构与创始人关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:Founder {id: 'ai:shen_founder'}) CREATE (a)-[:hasFounder {name: '有创始人'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:Founder {id: 'ai:xu_founder'}) CREATE (a)-[:hasFounder {name: '有创始人'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:Founder {id: 'ai:xiong_founder'}) CREATE (a)-[:hasFounder {name: '有创始人'}]->(b);

// 投资人与投资机构关系
MATCH (a:Investor {id: 'ai:shen'}), (b:AngelInvestor {id: 'ai:sequoia'}) CREATE (a)-[:manages {name: '管理'}]->(b);
MATCH (a:Investor {id: 'ai:xu'}), (b:AngelInvestor {id: 'ai:zhen'}) CREATE (a)-[:manages {name: '管理'}]->(b);
MATCH (a:Investor {id: 'ai:xiong'}), (b:VentureCapital {id: 'ai:idg'}) CREATE (a)-[:manages {name: '管理'}]->(b);

// 投资机构与投资事件关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:InvestmentEvent {id: 'ai:event1'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:InvestmentEvent {id: 'ai:event2'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:InvestmentEvent {id: 'ai:event3'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);

// 投资事件与投资轮次关系
MATCH (a:InvestmentEvent {id: 'ai:event1'}), (b:RoundA {id: 'ai:roundA'}) CREATE (a)-[:hasRound {name: '有轮次'}]->(b);
MATCH (a:InvestmentEvent {id: 'ai:event2'}), (b:AngelRound {id: 'ai:angel'}) CREATE (a)-[:hasRound {name: '有轮次'}]->(b);
MATCH (a:InvestmentEvent {id: 'ai:event3'}), (b:AngelRound {id: 'ai:angel'}) CREATE (a)-[:hasRound {name: '有轮次'}]->(b);

// 投资机构与投资领域关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:Education {id: 'ai:edu'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:Education {id: 'ai:edu'}) CREATE (a)-[:focusesOn {name: '关注领域'}]->(b);

// 投资机构与地理位置关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);

// 投资机构与公司关系
MATCH (a:AngelInvestor {id: 'ai:sequoia'}), (b:StartupCompany {id: 'ai:bytedance'}) CREATE (a)-[:invests {name: '投资'}]->(b);
MATCH (a:VentureCapital {id: 'ai:idg'}), (b:StartupCompany {id: 'ai:xiaomi'}) CREATE (a)-[:invests {name: '投资'}]->(b);
MATCH (a:AngelInvestor {id: 'ai:zhen'}), (b:StartupCompany {id: 'ai:meituan'}) CREATE (a)-[:invests {name: '投资'}]->(b);

// 公司与投资事件关系
MATCH (a:StartupCompany {id: 'ai:bytedance'}), (b:InvestmentEvent {id: 'ai:event1'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:StartupCompany {id: 'ai:xiaomi'}), (b:InvestmentEvent {id: 'ai:event2'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);
MATCH (a:StartupCompany {id: 'ai:meituan'}), (b:InvestmentEvent {id: 'ai:event3'}) CREATE (a)-[:hasInvestmentEvent {name: '有投资事件'}]->(b);

// 公司与地理位置关系
MATCH (a:StartupCompany {id: 'ai:bytedance'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:StartupCompany {id: 'ai:xiaomi'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);
MATCH (a:StartupCompany {id: 'ai:meituan'}), (b:City {id: 'ai:beijing'}) CREATE (a)-[:locatedIn {name: '位于'}]->(b);

// 公司与投资领域关系
MATCH (a:StartupCompany {id: 'ai:bytedance'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:belongsTo {name: '属于领域'}]->(b);
MATCH (a:StartupCompany {id: 'ai:xiaomi'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:belongsTo {name: '属于领域'}]->(b);
MATCH (a:StartupCompany {id: 'ai:meituan'}), (b:Technology {id: 'ai:tech'}) CREATE (a)-[:belongsTo {name: '属于领域'}]->(b);
