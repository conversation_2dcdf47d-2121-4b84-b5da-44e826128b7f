#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import argparse
import time
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('neo4j_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_command(command, shell=True):
    """
    运行命令并返回结果
    
    Args:
        command: 要运行的命令
        shell: 是否使用shell执行
        
    Returns:
        (returncode, stdout, stderr)
    """
    logger.info(f"执行命令: {command}")
    
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=shell,
            universal_newlines=True
        )
        
        stdout, stderr = process.communicate()
        returncode = process.returncode
        
        if returncode != 0:
            logger.error(f"命令执行失败，返回码: {returncode}")
            logger.error(f"错误信息: {stderr}")
        else:
            logger.info(f"命令执行成功")
            
        return returncode, stdout, stderr
    
    except Exception as e:
        logger.error(f"执行命令时出错: {e}")
        return -1, "", str(e)

def convert_data_to_csv():
    """
    运行数据转换脚本，将JSON数据转换为CSV文件
    
    Returns:
        是否成功
    """
    logger.info("步骤1: 转换数据为Neo4j可导入的CSV格式")
    
    # 检查转换脚本是否存在
    script_path = Path("src/convert_to_neo4j_csv.py")
    if not script_path.exists():
        logger.error(f"转换脚本不存在: {script_path}")
        return False
    
    # 运行转换脚本
    returncode, stdout, stderr = run_command(f"python {script_path}")
    
    if returncode != 0:
        logger.error("数据转换失败")
        return False
    
    # 检查CSV文件是否生成
    csv_dir = Path("neo4j_import")
    if not csv_dir.exists() or not any(csv_dir.glob("*.csv")):
        logger.error("未找到生成的CSV文件")
        return False
    
    logger.info("数据转换成功")
    return True

def import_to_neo4j(database_name, neo4j_home=None):
    """
    使用Neo4j批量导入工具导入CSV数据
    
    Args:
        database_name: 数据库名称
        neo4j_home: Neo4j安装路径
        
    Returns:
        是否成功
    """
    logger.info(f"步骤2: 导入数据到Neo4j数据库 {database_name}")
    
    # 构建neo4j-admin命令
    neo4j_admin = "neo4j-admin"
    if neo4j_home:
        if os.name == 'nt':  # Windows
            neo4j_admin = os.path.join(neo4j_home, "bin", "neo4j-admin.bat")
        else:  # Linux/Mac
            neo4j_admin = os.path.join(neo4j_home, "bin", "neo4j-admin")
    
    # 检查neo4j-admin是否可用
    returncode, stdout, stderr = run_command(f"{neo4j_admin} --version")
    if returncode != 0:
        logger.error(f"无法执行neo4j-admin命令，请确保Neo4j已安装并在PATH中")
        return False
    
    # 构建导入命令
    import_cmd = f'{neo4j_admin} import --database={database_name}'
    
    # 添加节点文件
    node_files = {
        "AngelInvestor": "neo4j_import/angel_investor.csv",
        "VentureCapital": "neo4j_import/venture_capital.csv",
        "StartupCompany": "neo4j_import/company.csv",
        "InvestmentRound": "neo4j_import/investment_round.csv",
        "InvestmentEvent": "neo4j_import/investment_event.csv"
    }
    
    for label, file_path in node_files.items():
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"节点文件不存在: {file_path}")
            continue
        
        # 使用适合当前操作系统的路径分隔符
        file_path = file_path.replace('/', os.path.sep)
        import_cmd += f' --nodes={label}={file_path}'
    
    # 添加关系文件
    relationship_files = {
        "INVESTS": "neo4j_import/invests_relationship.csv",
        "HAS_INVESTMENT_EVENT": "neo4j_import/has_event_relationship.csv",
        "HAS_ROUND": "neo4j_import/has_round_relationship.csv"
    }
    
    for rel_type, file_path in relationship_files.items():
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.warning(f"关系文件不存在: {file_path}")
            continue
        
        # 使用适合当前操作系统的路径分隔符
        file_path = file_path.replace('/', os.path.sep)
        import_cmd += f' --relationships={rel_type}={file_path}'
    
    # 添加其他参数
    import_cmd += ' --delimiter="," --array-delimiter=";" --id-type=STRING'
    
    # 执行导入命令
    returncode, stdout, stderr = run_command(import_cmd)
    
    if returncode != 0:
        logger.error("数据导入失败")
        return False
    
    logger.info("数据导入成功")
    return True

def update_neo4j_config(database_name, neo4j_home=None):
    """
    更新Neo4j配置，设置默认数据库
    
    Args:
        database_name: 数据库名称
        neo4j_home: Neo4j安装路径
        
    Returns:
        是否成功
    """
    logger.info(f"步骤3: 更新Neo4j配置，设置默认数据库为 {database_name}")
    
    # 确定配置文件路径
    if neo4j_home:
        config_path = os.path.join(neo4j_home, "conf", "neo4j.conf")
    else:
        # 尝试查找配置文件
        possible_paths = [
            "/etc/neo4j/neo4j.conf",  # Linux
            "/usr/local/Cellar/neo4j/*/libexec/conf/neo4j.conf",  # Mac (Homebrew)
            "C:/Program Files/Neo4j*/default.graphdb/conf/neo4j.conf",  # Windows
            "C:/Neo4j*/conf/neo4j.conf"  # Windows (自定义安装)
        ]
        
        config_path = None
        for path in possible_paths:
            # 处理通配符
            if '*' in path:
                import glob
                matches = glob.glob(path)
                if matches:
                    config_path = matches[0]
                    break
            elif os.path.exists(path):
                config_path = path
                break
    
    if not config_path or not os.path.exists(config_path):
        logger.warning(f"无法找到Neo4j配置文件，请手动设置默认数据库")
        logger.warning(f"请在neo4j.conf文件中添加或修改以下行:")
        logger.warning(f"dbms.default_database={database_name}")
        return False
    
    # 读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        config_lines = f.readlines()
    
    # 检查是否已经设置了默认数据库
    default_db_line = f"dbms.default_database={database_name}"
    default_db_pattern = "dbms.default_database="
    
    # 查找并更新或添加默认数据库设置
    found = False
    for i, line in enumerate(config_lines):
        if line.strip().startswith(default_db_pattern):
            config_lines[i] = default_db_line + "\n"
            found = True
            break
    
    if not found:
        config_lines.append("\n" + default_db_line + "\n")
    
    # 写回配置文件
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            f.writelines(config_lines)
        logger.info(f"已更新Neo4j配置文件: {config_path}")
        return True
    except Exception as e:
        logger.error(f"更新Neo4j配置文件时出错: {e}")
        logger.warning(f"请手动在neo4j.conf文件中添加或修改以下行:")
        logger.warning(f"dbms.default_database={database_name}")
        return False

def restart_neo4j(neo4j_home=None):
    """
    重启Neo4j服务
    
    Args:
        neo4j_home: Neo4j安装路径
        
    Returns:
        是否成功
    """
    logger.info("步骤4: 重启Neo4j服务")
    
    # 构建neo4j命令
    neo4j_cmd = "neo4j"
    if neo4j_home:
        if os.name == 'nt':  # Windows
            neo4j_cmd = os.path.join(neo4j_home, "bin", "neo4j.bat")
        else:  # Linux/Mac
            neo4j_cmd = os.path.join(neo4j_home, "bin", "neo4j")
    
    # 停止Neo4j服务
    logger.info("停止Neo4j服务")
    returncode, stdout, stderr = run_command(f"{neo4j_cmd} stop")
    
    # 等待服务停止
    time.sleep(5)
    
    # 启动Neo4j服务
    logger.info("启动Neo4j服务")
    returncode, stdout, stderr = run_command(f"{neo4j_cmd} start")
    
    if returncode != 0:
        logger.error("重启Neo4j服务失败")
        logger.warning("请手动重启Neo4j服务")
        return False
    
    logger.info("Neo4j服务已重启")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将数据导入到Neo4j数据库')
    parser.add_argument('--database', default='angelinvestment', help='数据库名称')
    parser.add_argument('--neo4j-home', help='Neo4j安装路径')
    parser.add_argument('--skip-convert', action='store_true', help='跳过数据转换步骤')
    parser.add_argument('--skip-config', action='store_true', help='跳过配置更新步骤')
    parser.add_argument('--skip-restart', action='store_true', help='跳过重启Neo4j服务步骤')
    
    args = parser.parse_args()
    
    logger.info("开始导入数据到Neo4j")
    logger.info(f"数据库名称: {args.database}")
    if args.neo4j_home:
        logger.info(f"Neo4j安装路径: {args.neo4j_home}")
    
    # 步骤1: 转换数据为CSV格式
    if not args.skip_convert:
        if not convert_data_to_csv():
            logger.error("数据转换失败，导入过程终止")
            return 1
    else:
        logger.info("跳过数据转换步骤")
    
    # 步骤2: 导入数据到Neo4j
    if not import_to_neo4j(args.database, args.neo4j_home):
        logger.error("数据导入失败，导入过程终止")
        return 1
    
    # 步骤3: 更新Neo4j配置
    if not args.skip_config:
        update_neo4j_config(args.database, args.neo4j_home)
    else:
        logger.info("跳过配置更新步骤")
    
    # 步骤4: 重启Neo4j服务
    if not args.skip_restart:
        restart_neo4j(args.neo4j_home)
    else:
        logger.info("跳过重启Neo4j服务步骤")
    
    logger.info("导入过程完成")
    logger.info("请访问 http://localhost:7474 查看导入的数据")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
