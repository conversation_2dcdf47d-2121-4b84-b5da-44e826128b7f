#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
中国天使投资知识图谱主程序
"""

import os
import sys
import argparse

def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='中国天使投资知识图谱系统')
    parser.add_argument('--action', choices=['crawl', 'parse', 'import', 'query'], 
                       help='选择要执行的操作')
    
    args = parser.parse_args()
    
    if args.action == 'crawl':
        print("启动百度百科爬虫...")
        from src.baidu_baike_crawler import main as crawl_main
        crawl_main()
    
    elif args.action == 'parse':
        print("解析投资者数据...")
        from src.parse_investor_data_enhanced import create_enhanced_csv_files
        create_enhanced_csv_files("models/angel_investors_processed.json")
    
    elif args.action == 'import':
        print("导入数据到Neo4j...")
        from src.import_enhanced_to_neo4j import main as import_main
        import_main()
    
    elif args.action == 'query':
        print("启动查询示例...")
        from src.query_examples import main as query_main
        query_main()
    
    else:
        print("中国天使投资知识图谱系统")
        print("=" * 50)
        print("可用操作:")
        print("  --action crawl   : 爬取百度百科数据")
        print("  --action parse   : 解析数据生成CSV文件")
        print("  --action import  : 导入数据到Neo4j")
        print("  --action query   : 查询示例")
        print("\n示例:")
        print("  python main.py --action parse")
        print("  python main.py --action import")

if __name__ == "__main__":
    main()
