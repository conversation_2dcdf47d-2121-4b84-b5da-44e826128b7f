#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time

def print_header(title):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def main():
    print_header("中国天使投资知识图谱构建")

    # 步骤1：解析天使投资公司数据源
    print_header("步骤1：解析天使投资公司数据源")
    print("正在解析天使投资公司数据源...")

    try:
        import src.parse_investor_data as parse_investor_data
        angel_investors = parse_investor_data.main()

        if not angel_investors:
            print("解析天使投资公司数据源失败，程序终止")
            return

        print("天使投资公司数据源解析完成")
    except Exception as e:
        print(f"解析天使投资公司数据源时出错: {e}")
        return

    # 步骤2：通过百度百科检索和解析投资公司信息
    print_header("步骤2：通过百度百科检索和解析投资公司信息")
    print("正在通过百度百科检索和解析投资公司信息...")

    try:
        import src.baidu_baike_crawler as baidu_baike_crawler
        baidu_baike_crawler.main()
        print("百度百科信息检索和解析完成")
    except Exception as e:
        print(f"通过百度百科检索和解析投资公司信息时出错: {e}")
        return

    # 步骤3：构建知识图谱
    print_header("步骤3：构建知识图谱")
    print("正在构建知识图谱...")

    try:
        import src.build_knowledge_graph as build_knowledge_graph
        build_knowledge_graph.main()
        print("知识图谱构建完成")
    except Exception as e:
        print(f"构建知识图谱时出错: {e}")
        return

    print_header("知识图谱构建流程完成")
    print("已生成以下文件:")
    print("  - models/angel_investors.json: 解析的天使投资公司数据")
    print("  - models/angel_investors_with_baike.json: 带有百度百科信息的天使投资公司数据")
    print("  - models/angel_investment_knowledge_graph.json: 知识图谱数据")
    print("  - data/baidu_baike_data/: 每家公司的百度百科数据")

if __name__ == "__main__":
    main()
