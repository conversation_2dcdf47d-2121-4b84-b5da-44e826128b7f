#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from bilstm_crf_ner import NERModel

# 设置随机种子，确保结果可复现
np.random.seed(42)

def load_data(data_path):
    """
    加载NER数据
    
    Args:
        data_path: 数据文件路径
        
    Returns:
        sentences: 句子列表，每个句子是词语列表
        tags: 标签列表，每个标签列表对应一个句子
    """
    sentences = []
    tags = []
    
    with open(data_path, 'r', encoding='utf-8') as f:
        sentence = []
        tag_list = []
        
        for line in f:
            line = line.strip()
            
            if not line:  # 空行表示句子结束
                if sentence:
                    sentences.append(sentence)
                    tags.append(tag_list)
                    sentence = []
                    tag_list = []
            else:
                parts = line.split()
                if len(parts) >= 2:
                    word = parts[0]
                    tag = parts[-1]
                    sentence.append(word)
                    tag_list.append(tag)
        
        # 处理最后一个句子
        if sentence:
            sentences.append(sentence)
            tags.append(tag_list)
    
    return sentences, tags

def create_sample_data():
    """
    创建示例数据用于训练
    
    Returns:
        sentences: 句子列表，每个句子是词语列表
        tags: 标签列表，每个标签列表对应一个句子
    """
    # 创建示例数据目录
    os.makedirs('data/ner', exist_ok=True)
    
    # 示例数据 - 使用BIO标注方案
    # B-ORG: 组织实体的开始
    # I-ORG: 组织实体的内部
    # B-PER: 人物实体的开始
    # I-PER: 人物实体的内部
    # B-LOC: 地点实体的开始
    # I-LOC: 地点实体的内部
    # B-TIME: 时间实体的开始
    # I-TIME: 时间实体的内部
    # O: 非实体
    
    sample_data = [
        # 句子1
        [
            ("红杉", "B-ORG"),
            ("资本", "I-ORG"),
            ("是", "O"),
            ("一家", "O"),
            ("知名", "O"),
            ("的", "O"),
            ("风险", "O"),
            ("投资", "O"),
            ("公司", "O"),
            ("，", "O"),
            ("由", "O"),
            ("张", "B-PER"),
            ("磊", "I-PER"),
            ("于", "O"),
            ("2005年", "B-TIME"),
            ("在", "O"),
            ("北京", "B-LOC"),
            ("创立", "O"),
            ("。", "O")
        ],
        # 句子2
        [
            ("IDG", "B-ORG"),
            ("资本", "I-ORG"),
            ("成立", "O"),
            ("于", "O"),
            ("1992年", "B-TIME"),
            ("，", "O"),
            ("总部", "O"),
            ("位于", "O"),
            ("美国", "B-LOC"),
            ("波士顿", "B-LOC"),
            ("，", "O"),
            ("是", "O"),
            ("最早", "O"),
            ("进入", "O"),
            ("中国", "B-LOC"),
            ("的", "O"),
            ("外资", "O"),
            ("风投", "O"),
            ("机构", "O"),
            ("之一", "O"),
            ("。", "O")
        ],
        # 句子3
        [
            ("真格", "B-ORG"),
            ("基金", "I-ORG"),
            ("由", "O"),
            ("徐", "B-PER"),
            ("小平", "I-PER"),
            ("创立", "O"),
            ("，", "O"),
            ("专注", "O"),
            ("于", "O"),
            ("早期", "O"),
            ("投资", "O"),
            ("。", "O")
        ],
        # 句子4
        [
            ("腾讯", "B-ORG"),
            ("投资", "I-ORG"),
            ("是", "O"),
            ("腾讯", "B-ORG"),
            ("集团", "I-ORG"),
            ("的", "O"),
            ("投资", "O"),
            ("部门", "O"),
            ("，", "O"),
            ("成立", "O"),
            ("于", "O"),
            ("2008年", "B-TIME"),
            ("。", "O")
        ],
        # 句子5
        [
            ("经纬", "B-ORG"),
            ("中国", "I-ORG"),
            ("成立", "O"),
            ("于", "O"),
            ("2008年", "B-TIME"),
            ("，", "O"),
            ("由", "O"),
            ("张", "B-PER"),
            ("颖", "I-PER"),
            ("领导", "O"),
            ("，", "O"),
            ("总部", "O"),
            ("在", "O"),
            ("北京", "B-LOC"),
            ("。", "O")
        ],
        # 句子6
        [
            ("创新", "B-ORG"),
            ("工场", "I-ORG"),
            ("由", "O"),
            ("李", "B-PER"),
            ("开复", "I-PER"),
            ("于", "O"),
            ("2009年", "B-TIME"),
            ("创办", "O"),
            ("，", "O"),
            ("总部", "O"),
            ("位于", "O"),
            ("北京", "B-LOC"),
            ("中关村", "B-LOC"),
            ("。", "O")
        ],
        # 句子7
        [
            ("高瓴", "B-ORG"),
            ("资本", "I-ORG"),
            ("由", "O"),
            ("张", "B-PER"),
            ("磊", "I-PER"),
            ("于", "O"),
            ("2005年", "B-TIME"),
            ("创立", "O"),
            ("，", "O"),
            ("是", "O"),
            ("一家", "O"),
            ("专注", "O"),
            ("于", "O"),
            ("长期", "O"),
            ("投资", "O"),
            ("的", "O"),
            ("公司", "O"),
            ("。", "O")
        ],
        # 句子8
        [
            ("金沙江", "B-ORG"),
            ("创投", "I-ORG"),
            ("成立", "O"),
            ("于", "O"),
            ("2007年", "B-TIME"),
            ("，", "O"),
            ("由", "O"),
            ("朱", "B-PER"),
            ("啸", "I-PER"),
            ("虎", "I-PER"),
            ("创办", "O"),
            ("。", "O")
        ],
        # 句子9
        [
            ("阿里巴巴", "B-ORG"),
            ("创投", "I-ORG"),
            ("是", "O"),
            ("阿里巴巴", "B-ORG"),
            ("集团", "I-ORG"),
            ("旗下", "O"),
            ("的", "O"),
            ("投资", "O"),
            ("部门", "O"),
            ("，", "O"),
            ("关注", "O"),
            ("电子商务", "O"),
            ("和", "O"),
            ("互联网", "O"),
            ("领域", "O"),
            ("。", "O")
        ],
        # 句子10
        [
            ("深创投", "B-ORG"),
            ("成立", "O"),
            ("于", "O"),
            ("1999年", "B-TIME"),
            ("，", "O"),
            ("总部", "O"),
            ("位于", "O"),
            ("深圳", "B-LOC"),
            ("，", "O"),
            ("是", "O"),
            ("中国", "B-LOC"),
            ("最大", "O"),
            ("的", "O"),
            ("本土", "O"),
            ("风投", "O"),
            ("机构", "O"),
            ("之一", "O"),
            ("。", "O")
        ]
    ]
    
    # 将示例数据写入文件
    with open('data/ner/sample_data.txt', 'w', encoding='utf-8') as f:
        for sentence in sample_data:
            for word, tag in sentence:
                f.write(f"{word} {tag}\n")
            f.write("\n")
    
    # 准备返回的数据格式
    sentences = [[word for word, _ in sentence] for sentence in sample_data]
    tags = [[tag for _, tag in sentence] for sentence in sample_data]
    
    return sentences, tags

def plot_training_history(history):
    """
    绘制训练历史
    
    Args:
        history: 训练历史
    """
    plt.figure(figsize=(12, 4))
    
    # 绘制损失
    plt.subplot(1, 2, 1)
    plt.plot(history.history['loss'], label='Training Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('Model Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    # 绘制准确率
    plt.subplot(1, 2, 2)
    plt.plot(history.history['crf_accuracy'], label='Training Accuracy')
    plt.plot(history.history['val_crf_accuracy'], label='Validation Accuracy')
    plt.title('Model Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('training_history.png')
    plt.close()

def main():
    # 创建示例数据
    print("创建示例数据...")
    sentences, tags = create_sample_data()
    
    # 划分训练集和测试集
    print("划分数据集...")
    X_train, X_test, y_train, y_test = train_test_split(sentences, tags, test_size=0.2, random_state=42)
    
    # 初始化模型
    print("初始化模型...")
    model = NERModel(max_len=50, embedding_dim=100, lstm_units=64)
    
    # 准备数据
    print("准备数据...")
    X_train_padded, y_train_padded = model.prepare_data(X_train, y_train)
    X_test_padded, y_test_padded = model.prepare_data(X_test, y_test)
    
    # 构建模型
    print("构建模型...")
    model.build_model()
    model.model.summary()
    
    # 训练模型
    print("训练模型...")
    history = model.train(
        X_train_padded, y_train_padded,
        X_test_padded, y_test_padded,
        epochs=50,
        batch_size=2
    )
    
    # 绘制训练历史
    print("绘制训练历史...")
    plot_training_history(history)
    
    # 评估模型
    print("评估模型...")
    metrics = model.evaluate(X_test_padded, y_test_padded)
    
    print(f"Precision: {metrics['precision']:.4f}")
    print(f"Recall: {metrics['recall']:.4f}")
    print(f"F1 Score: {metrics['f1']:.4f}")
    print("\nClassification Report:")
    print(metrics['report'])
    
    # 保存模型和词汇表
    print("保存模型和词汇表...")
    model.save('ner_model.h5', 'ner_vocab.json')
    
    print("训练完成！")

if __name__ == "__main__":
    main()
