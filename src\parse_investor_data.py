import json
import os
import re
import uuid
import csv
from collections import defaultdict

def load_json_data(file_path):
    """
    加载JSON数据文件，处理可能的编码问题

    Args:
        file_path: JSON文件路径

    Returns:
        解析后的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except UnicodeDecodeError:
        # 尝试使用不同的编码
        with open(file_path, 'r', encoding='gbk') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"加载JSON文件时出错: {e}")
        # 尝试手动修复编码问题
        with open(file_path, 'rb') as f:
            content = f.read()
        # 尝试检测编码
        try:
            content = content.decode('utf-8')
        except UnicodeDecodeError:
            try:
                content = content.decode('gbk')
            except UnicodeDecodeError:
                # 如果还是失败，尝试忽略错误
                content = content.decode('utf-8', errors='ignore')

        # 修复可能的JSON格式问题
        content = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', content)
        return json.loads(content)

def clean_string(s):
    """
    清理字符串，确保CSV格式正确

    Args:
        s: 输入字符串

    Returns:
        清理后的字符串
    """
    if s is None:
        return ""
    return str(s).replace('"', '""').replace('\n', ' ').replace('\r', ' ').strip()

def generate_id(prefix, name):
    """
    生成唯一ID

    Args:
        prefix: ID前缀
        name: 名称

    Returns:
        生成的ID
    """
    # 使用名称的拼音或英文作为ID的一部分
    clean_name = re.sub(r'[^\w]', '', name)
    # 如果名称为空，使用UUID
    if not clean_name:
        clean_name = str(uuid.uuid4())[:8]
    return f"{prefix}_{clean_name}"

def extract_founding_date(info):
    """
    从公司信息中提取成立日期

    Args:
        info: 公司信息字典

    Returns:
        成立日期字符串
    """
    # 尝试不同的可能的键名
    date_keys = ['成立时间', '创立时间', '成立日期', 'foundingDate']
    for key in date_keys:
        if key in info:
            return info[key]
    return ""

def extract_description(info):
    """
    从公司信息中提取描述信息

    Args:
        info: 公司信息字典

    Returns:
        描述信息字符串
    """
    # 如果有直接的描述字段，使用它
    if 'description' in info:
        return info['description']

    # 否则，尝试构建描述
    description_parts = []

    # 添加公司类型
    if '公司类型' in info:
        description_parts.append(f"公司类型: {info['公司类型']}")

    # 添加所属行业
    if '所属行业' in info:
        description_parts.append(f"所属行业: {info['所属行业']}")

    # 添加经营范围
    if '经营范围' in info:
        description_parts.append(f"经营范围: {info['经营范围']}")

    # 添加总部地点
    if '总部地点' in info:
        description_parts.append(f"总部地点: {info['总部地点']}")

    # 添加公司口号
    if '公司口号' in info:
        description_parts.append(f"公司口号: {info['公司口号']}")

    # 添加年营业额
    if '年营业额' in info:
        description_parts.append(f"年营业额: {info['年营业额']}")

    # 添加员工数
    if '员工数' in info:
        description_parts.append(f"员工数: {info['员工数']}")

    # 添加创始人
    if '创始人' in info:
        description_parts.append(f"创始人: {info['创始人']}")

    # 添加法定代表人
    if '法定代表人' in info:
        description_parts.append(f"法定代表人: {info['法定代表人']}")

    # 添加注册资本
    if '注册资本' in info:
        description_parts.append(f"注册资本: {info['注册资本']}")

    # 添加外文名
    if '外文名' in info:
        description_parts.append(f"外文名: {info['外文名']}")

    # 将所有部分连接成一个字符串
    return "; ".join(description_parts)

def is_angel_investor(company_name, info):
    """
    判断是否为天使投资机构

    Args:
        company_name: 公司名称
        info: 公司信息字典

    Returns:
        是否为天使投资机构
    """
    # 根据名称判断
    if '天使' in company_name or 'angel' in company_name.lower():
        return True

    # 根据公司类型判断
    if '公司类型' in info and ('创投' in info['公司类型'] or '投资' in info['公司类型']):
        return True

    # 根据经营范围判断
    if '经营范围' in info and ('创业投资' in info['经营范围'] or '天使投资' in info['经营范围']):
        return True

    # 根据其他特征判断
    if '投资' in company_name or '创投' in company_name or '资本' in company_name or 'capital' in company_name.lower():
        return True

    # 根据公司名称中是否包含"基金"判断
    if '基金' in company_name or 'fund' in company_name.lower():
        return True

    # 默认为风险投资机构
    return False

def parse_investor_data(input_file, output_dir, investment_dict=None, company_dict=None):
    """
    解析投资者数据并生成CSV文件

    Args:
        input_file: 输入JSON文件路径
        output_dir: 输出目录
        investment_dict: 真实投资关系数据字典，键为投资者名称，值为被投资公司列表
        company_dict: 真实公司数据字典，键为公司ID，值为公司信息

    Returns:
        生成的CSV文件路径列表
    """
    # 检查是否使用真实数据
    use_real_data = investment_dict is not None and company_dict is not None
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")

    # 加载数据
    print(f"正在解析数据文件: {input_file}")
    data = load_json_data(input_file)

    # 检查数据结构
    if not isinstance(data, list):
        print(f"数据结构不符合预期，应为列表，实际为: {type(data)}")
        return []

    print(f"共加载 {len(data)} 条投资者数据")

    # 创建CSV文件
    angel_investor_file = os.path.join(output_dir, 'angel_investor.csv')
    venture_capital_file = os.path.join(output_dir, 'venture_capital.csv')
    company_file = os.path.join(output_dir, 'company.csv')
    investment_round_file = os.path.join(output_dir, 'investment_round.csv')
    investment_event_file = os.path.join(output_dir, 'investment_event.csv')
    invests_relationship_file = os.path.join(output_dir, 'invests_relationship.csv')
    has_event_relationship_file = os.path.join(output_dir, 'has_event_relationship.csv')
    has_round_relationship_file = os.path.join(output_dir, 'has_round_relationship.csv')

    # 定义CSV字段
    investor_fields = ['id:ID', 'name', 'foundingDate', 'description', ':LABEL']
    company_fields = ['id:ID', 'name', 'foundingDate', 'description', ':LABEL']
    round_fields = ['id:ID', 'name', 'description', ':LABEL']
    event_fields = ['id:ID', 'name', 'investmentAmount', 'investmentDate', 'description', ':LABEL']
    invests_fields = [':START_ID', ':END_ID', 'name', ':TYPE']
    has_event_fields = [':START_ID', ':END_ID', 'name', ':TYPE']
    has_round_fields = [':START_ID', ':END_ID', 'name', ':TYPE']

    # 创建CSV写入器，使用utf-8-sig编码以支持中文
    with open(angel_investor_file, 'w', newline='', encoding='utf-8-sig') as angel_f, \
         open(venture_capital_file, 'w', newline='', encoding='utf-8-sig') as vc_f, \
         open(company_file, 'w', newline='', encoding='utf-8-sig') as company_f, \
         open(investment_round_file, 'w', newline='', encoding='utf-8-sig') as round_f, \
         open(investment_event_file, 'w', newline='', encoding='utf-8-sig') as event_f, \
         open(invests_relationship_file, 'w', newline='', encoding='utf-8-sig') as invests_f, \
         open(has_event_relationship_file, 'w', newline='', encoding='utf-8-sig') as has_event_f, \
         open(has_round_relationship_file, 'w', newline='', encoding='utf-8-sig') as has_round_f:

        # 创建CSV写入器
        angel_writer = csv.DictWriter(angel_f, fieldnames=investor_fields)
        vc_writer = csv.DictWriter(vc_f, fieldnames=investor_fields)
        company_writer = csv.DictWriter(company_f, fieldnames=company_fields)
        round_writer = csv.DictWriter(round_f, fieldnames=round_fields)
        event_writer = csv.DictWriter(event_f, fieldnames=event_fields)
        invests_writer = csv.DictWriter(invests_f, fieldnames=invests_fields)
        has_event_writer = csv.DictWriter(has_event_f, fieldnames=has_event_fields)
        has_round_writer = csv.DictWriter(has_round_f, fieldnames=has_round_fields)

        # 写入CSV头
        angel_writer.writeheader()
        vc_writer.writeheader()
        company_writer.writeheader()
        round_writer.writeheader()
        event_writer.writeheader()
        invests_writer.writeheader()
        has_event_writer.writeheader()
        has_round_writer.writeheader()

        # 预定义投资轮次
        predefined_rounds = [
            '天使轮', '种子轮', 'Pre-A轮', 'A轮', 'A+轮',
            'B轮', 'B+轮', 'C轮', 'D轮', 'E轮', 'F轮',
            'Pre-IPO', '新三板定增', '战略投资', '并购', '未知轮次'
        ]

        # 轮次映射到标签
        round_labels = {
            '天使轮': 'AngelRound',
            '种子轮': 'SeedRound',
            'Pre-A轮': 'PreARound',
            'A轮': 'RoundA',
            'A+轮': 'RoundAPlus',
            'B轮': 'RoundB',
            'B+轮': 'RoundBPlus',
            'C轮': 'RoundC',
            'D轮': 'RoundD',
            'E轮': 'RoundE',
            'F轮': 'RoundF',
            'Pre-IPO': 'PreIPO',
            '新三板定增': 'NEEQ',
            '战略投资': 'StrategicInvestment',
            '并购': 'Acquisition',
            '未知轮次': 'UnknownRound'
        }

        # 轮次ID映射，确保每个轮次有唯一的ID
        round_ids = {
            '天使轮': 'round_天使轮',
            '种子轮': 'round_种子轮',
            'Pre-A轮': 'round_PreA轮',
            'A轮': 'round_A轮',
            'A+轮': 'round_APlus轮',
            'B轮': 'round_B轮',
            'B+轮': 'round_BPlus轮',
            'C轮': 'round_C轮',
            'D轮': 'round_D轮',
            'E轮': 'round_E轮',
            'F轮': 'round_F轮',
            'Pre-IPO': 'round_PreIPO',
            '新三板定增': 'round_新三板定增',
            '战略投资': 'round_战略投资',
            '并购': 'round_并购',
            '未知轮次': 'round_未知轮次'
        }

        # 写入轮次数据
        for round_name in predefined_rounds:
            round_id = round_ids.get(round_name, generate_id('round', round_name))
            label = round_labels.get(round_name, 'InvestmentRound')

            round_writer.writerow({
                'id:ID': round_id,
                'name': clean_string(round_name),
                'description': f"{round_name}融资",
                ':LABEL': label
            })

        # 用于跟踪已处理的公司，避免重复
        processed_companies = set()

        # 事件ID计数器
        event_id = 1

        # 处理每个投资者
        for item in data:
            if 'company_name' in item and 'info' in item:
                # 提取投资者名称和信息
                investor_name = item['company_name']
                info = item['info']

                # 生成投资者ID
                investor_id = generate_id('investor', investor_name)

                # 提取成立日期和描述
                founding_date = extract_founding_date(info)
                description = extract_description(info)

                # 判断是否为天使投资机构
                is_angel = is_angel_investor(investor_name, info)

                # 写入投资者数据
                investor_data = {
                    'id:ID': investor_id,
                    'name': clean_string(investor_name),
                    'foundingDate': clean_string(founding_date),
                    'description': clean_string(description),
                    ':LABEL': 'AngelInvestor' if is_angel else 'VentureCapital'
                }

                if is_angel:
                    angel_writer.writerow(investor_data)
                else:
                    vc_writer.writerow(investor_data)

                # 确定投资轮次
                investment_round = "未知轮次"
                # 如果是天使投资机构，更可能进行天使轮投资
                if is_angel:
                    investment_round = "天使轮"
                # 如果公司名称中包含某些关键词，可能暗示投资轮次
                elif '早期' in investor_name:
                    investment_round = "种子轮"
                elif 'A轮' in investor_name:
                    investment_round = "A轮"
                elif 'B轮' in investor_name:
                    investment_round = "B轮"
                elif 'C轮' in investor_name:
                    investment_round = "C轮"
                elif 'D轮' in investor_name:
                    investment_round = "D轮"

                # 获取轮次ID
                round_id = round_ids.get(investment_round, generate_id('round', investment_round))

                # 使用真实数据或生成虚拟数据
                if use_real_data and investor_name in investment_dict and investment_dict[investor_name]:
                    # 使用真实数据
                    # 获取该投资者的第一个投资关系
                    investment = investment_dict[investor_name][0]
                    company_id = f"company_{investment['company_id']}"
                    company_name = investment['company_name']
                    investment_round = investment['round']
                    investment_date = investment['date']

                    # 获取公司信息
                    company_info = company_dict.get(investment['company_id'], {})
                    company_description = company_info.get('description', f"{company_name}，获得{investor_name}投资")
                    company_founding_date = company_info.get('foundingDate', founding_date)

                    # 避免重复
                    if company_id not in processed_companies:
                        processed_companies.add(company_id)

                        # 写入公司数据
                        company_writer.writerow({
                            'id:ID': company_id,
                            'name': clean_string(company_name),
                            'foundingDate': clean_string(company_founding_date),
                            'description': clean_string(company_description),
                            ':LABEL': 'StartupCompany'
                        })

                    # 更新轮次ID
                    round_id = round_ids.get(investment_round, generate_id('round', investment_round))

                    # 创建投资事件
                    event_unique_id = f"event_{event_id}"
                    event_name = f"{investor_name}投资{company_name}"

                    # 写入投资事件数据
                    event_writer.writerow({
                        'id:ID': event_unique_id,
                        'name': clean_string(event_name),
                        'investmentAmount': '',  # 没有金额信息
                        'investmentDate': clean_string(investment_date),
                        'description': f"{investor_name}对{company_name}的{investment_round}投资",
                        ':LABEL': 'InvestmentEvent'
                    })

                    # 写入投资关系
                    invests_writer.writerow({
                        ':START_ID': investor_id,
                        ':END_ID': company_id,
                        'name': '投资',
                        ':TYPE': 'INVESTS'
                    })

                    # 写入投资机构与投资事件的关系
                    has_event_writer.writerow({
                        ':START_ID': investor_id,
                        ':END_ID': event_unique_id,
                        'name': '有投资事件',
                        ':TYPE': 'HAS_INVESTMENT_EVENT'
                    })

                    # 写入公司与投资事件的关系
                    has_event_writer.writerow({
                        ':START_ID': company_id,
                        ':END_ID': event_unique_id,
                        'name': '有投资事件',
                        ':TYPE': 'HAS_INVESTMENT_EVENT'
                    })

                    # 写入投资事件与轮次的关系
                    has_round_writer.writerow({
                        ':START_ID': event_unique_id,
                        ':END_ID': round_id,
                        'name': '有轮次',
                        ':TYPE': 'HAS_ROUND'
                    })

                    # 从投资字典中移除已使用的投资关系
                    if investor_name in investment_dict and investment_dict[investor_name]:
                        investment_dict[investor_name].pop(0)
                else:
                    # 使用虚拟数据
                    # 创建一个虚拟的被投资公司
                    industry = ""
                    company_type = ""
                    location = ""
                    business = ""

                    # 提取行业信息
                    if '所属行业' in info:
                        industry = info['所属行业']
                    elif '经营范围' in info:
                        # 从经营范围中提取可能的行业
                        business_scope = info['经营范围']
                        # 常见行业关键词
                        industry_keywords = ['科技', '医疗', '教育', '金融', '互联网', '软件', '硬件',
                                            '人工智能', '大数据', '云计算', '物联网', '新能源', '制造',
                                            '零售', '服务', '咨询', '文化', '娱乐', '游戏', '电商']
                        for keyword in industry_keywords:
                            if keyword in business_scope:
                                industry = keyword
                                break

                    # 提取公司类型
                    if '公司类型' in info:
                        company_type = info['公司类型']

                    # 提取地点信息
                    if '总部地点' in info:
                        location = info['总部地点']

                    # 提取经营范围
                    if '经营范围' in info:
                        business = info['经营范围']

                    # 如果没有找到行业信息，使用一个通用的行业
                    if not industry:
                        industry = "科技"

                    # 生成公司名称前缀
                    company_prefix = ""
                    if location:
                        # 提取地点的第一个字符（如"北京"取"北"）
                        location_prefix = location[:1]
                        company_prefix = f"{location_prefix}"

                    # 生成公司名称后缀
                    company_suffix = ""
                    if '有限公司' in company_type:
                        company_suffix = "有限公司"
                    elif '股份公司' in company_type or '股份有限公司' in company_type:
                        company_suffix = "股份有限公司"
                    else:
                        company_suffix = "科技有限公司"

                    # 生成被投资公司名称
                    invested_company_name = f"{company_prefix}{industry}创新{event_id}{company_suffix}"
                    company_id = generate_id('company', invested_company_name)

                    # 避免重复
                    if company_id not in processed_companies:
                        processed_companies.add(company_id)

                        # 生成公司描述
                        company_description = f"{industry}领域的创新企业"
                        if business:
                            company_description += f"，经营范围包括{business}"
                        if location:
                            company_description += f"，总部位于{location}"
                        company_description += f"，获得{investor_name}投资"

                        # 写入公司数据
                        company_writer.writerow({
                            'id:ID': company_id,
                            'name': clean_string(invested_company_name),
                            'foundingDate': clean_string(founding_date),
                            'description': clean_string(company_description),
                            ':LABEL': 'StartupCompany'
                        })

                # 如果使用虚拟数据，需要创建投资事件
                if not use_real_data or investor_name not in investment_dict or not investment_dict[investor_name]:
                    # 创建投资事件
                    event_unique_id = f"event_{event_id}"
                    # 确保invested_company_name已定义
                    if 'invested_company_name' in locals():
                        event_name = f"{investor_name}投资{invested_company_name}"
                    else:
                        event_name = f"{investor_name}的投资事件"

                    # 写入投资事件数据
                    # 确保invested_company_name已定义
                    if 'invested_company_name' in locals():
                        description = f"{investor_name}对{invested_company_name}的{investment_round}投资"
                    else:
                        description = f"{investor_name}的{investment_round}投资"

                    event_writer.writerow({
                        'id:ID': event_unique_id,
                        'name': clean_string(event_name),
                        'investmentAmount': '',  # 没有金额信息
                        'investmentDate': clean_string(founding_date),
                        'description': clean_string(description),
                        ':LABEL': 'InvestmentEvent'
                    })

                    # 写入投资关系
                    invests_writer.writerow({
                        ':START_ID': investor_id,
                        ':END_ID': company_id,
                        'name': '投资',
                        ':TYPE': 'INVESTS'
                    })

                    # 写入投资机构与投资事件的关系
                    has_event_writer.writerow({
                        ':START_ID': investor_id,
                        ':END_ID': event_unique_id,
                        'name': '有投资事件',
                        ':TYPE': 'HAS_INVESTMENT_EVENT'
                    })

                    # 写入公司与投资事件的关系
                    has_event_writer.writerow({
                        ':START_ID': company_id,
                        ':END_ID': event_unique_id,
                        'name': '有投资事件',
                        ':TYPE': 'HAS_INVESTMENT_EVENT'
                    })

                    # 写入投资事件与轮次的关系
                    has_round_writer.writerow({
                        ':START_ID': event_unique_id,
                        ':END_ID': round_id,
                        'name': '有轮次',
                        ':TYPE': 'HAS_ROUND'
                    })

                # 增加事件ID计数器
                event_id += 1

    # 返回生成的CSV文件路径
    csv_files = [
        angel_investor_file,
        venture_capital_file,
        company_file,
        investment_round_file,
        investment_event_file,
        invests_relationship_file,
        has_event_relationship_file,
        has_round_relationship_file
    ]

    # 打印统计信息
    print(f"\n共处理 {len(data)} 家投资机构")
    print(f"生成 {len(processed_companies)} 家公司数据")
    print(f"生成 {event_id - 1} 个投资事件")
    print(f"生成 {len(predefined_rounds)} 种投资轮次")

    return csv_files

def load_real_investment_data(investment_file):
    """
    加载真实的投资关系数据

    Args:
        investment_file: 投资关系数据文件路径

    Returns:
        投资关系数据字典，键为投资者名称，值为被投资公司列表
    """
    if not os.path.exists(investment_file):
        print(f"投资关系数据文件不存在: {investment_file}")
        return {}

    try:
        with open(investment_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 构建投资关系字典
        investment_dict = defaultdict(list)
        for inv in data:
            investor_name = inv['investor_name']
            company_id = inv['company_id']
            company_name = inv['company_name']
            round_name = inv['round']
            date = inv['date']

            investment_dict[investor_name].append({
                'company_id': company_id,
                'company_name': company_name,
                'round': round_name,
                'date': date
            })

        return investment_dict
    except Exception as e:
        print(f"加载投资关系数据失败: {e}")
        return {}

def load_real_company_data(company_file):
    """
    加载真实的公司数据

    Args:
        company_file: 公司数据文件路径

    Returns:
        公司数据字典，键为公司ID，值为公司信息
    """
    if not os.path.exists(company_file):
        print(f"公司数据文件不存在: {company_file}")
        return {}

    try:
        # 尝试使用不同的编码读取文件
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312', 'latin-1']
        company_dict = {}

        for encoding in encodings:
            try:
                with open(company_file, 'r', encoding=encoding) as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        company_dict[row['id']] = {
                            'name': row['name'],
                            'description': row['description'],
                            'foundingDate': row['foundingDate']
                        }
                print(f"使用 {encoding} 编码成功读取了 {len(company_dict)} 条公司数据")
                break
            except UnicodeDecodeError:
                print(f"使用 {encoding} 编码读取失败，尝试下一种编码")
                continue
            except Exception as e:
                print(f"使用 {encoding} 编码读取时出错: {e}")
                continue

        if not company_dict:
            print("所有编码都无法读取文件")
            return {}

        return company_dict
    except Exception as e:
        print(f"加载公司数据失败: {e}")
        return {}

def main():
    # 数据文件路径
    input_file = os.path.join('models', 'angel_investors_processed.json')
    output_dir = os.path.join('neo4j_import')

    # 真实投资数据文件路径
    investment_file = os.path.join('data', 'extracted', 'unique_investments.json')
    company_file = os.path.join('data', 'extracted', 'company.csv')

    # 检查是否存在真实投资数据
    use_real_data = os.path.exists(investment_file) and os.path.exists(company_file)

    if use_real_data:
        print(f"找到真实投资数据文件，将使用真实数据替代虚拟数据")
        # 加载真实投资数据
        investment_dict = load_real_investment_data(investment_file)
        company_dict = load_real_company_data(company_file)

        # 解析数据并生成CSV文件，使用真实数据
        csv_files = parse_investor_data(input_file, output_dir, investment_dict, company_dict)
    else:
        print(f"未找到真实投资数据文件，将使用虚拟数据")
        # 解析数据并生成CSV文件，使用虚拟数据
        csv_files = parse_investor_data(input_file, output_dir)

    # 输出结果
    if csv_files:
        print("\n已生成以下CSV文件:")
        for file_path in csv_files:
            print(f"- {file_path}")
        print("\n这些CSV文件可以直接导入到Neo4j数据库中，构建知识图谱。")
    else:
        print("\n生成CSV文件失败，请检查输入数据。")

if __name__ == "__main__":
    main()


