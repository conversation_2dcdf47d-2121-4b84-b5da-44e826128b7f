#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
from collections import defaultdict

def load_json_data(file_path):
    """
    加载JSON数据文件，处理可能的编码问题

    Args:
        file_path: JSON文件路径

    Returns:
        解析后的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except UnicodeDecodeError:
        # 尝试使用不同的编码
        with open(file_path, 'r', encoding='gbk') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"加载JSON文件时出错: {e}")
        # 尝试手动修复编码问题
        with open(file_path, 'rb') as f:
            content = f.read()
        # 尝试检测编码
        try:
            content = content.decode('utf-8')
        except UnicodeDecodeError:
            try:
                content = content.decode('gbk')
            except UnicodeDecodeError:
                # 如果还是失败，尝试忽略错误
                content = content.decode('utf-8', errors='ignore')

        # 修复可能的JSON格式问题
        content = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', content)
        return json.loads(content)

def extract_angel_investors(data):
    """
    从数据中提取天使投资公司信息

    Args:
        data: 解析后的JSON数据

    Returns:
        天使投资公司列表
    """
    angel_investors = []

    # 检查数据结构
    if '@graph' not in data:
        print("数据结构不符合预期，找不到@graph字段")
        return angel_investors

    # 遍历图中的所有节点
    for node in data['@graph']:
        # 检查节点是否为投资者类型
        if '@type' in node and node['@type'] == 'investor':
            # 提取投资者名称
            if 'name' in node:
                investor_name = node['name']

                # 检查是否有投资关系
                if 'relationship' in node and 'investCompany' in node['relationship']:
                    # 获取投资的公司列表
                    invested_companies = []
                    for company in node['relationship']['investCompany']:
                        if '@type' in company and company['@type'] == 'company':
                            company_info = {
                                'id': company.get('@id', ''),
                                'round': company.get('round', ''),
                                'date': company.get('date', '')
                            }
                            invested_companies.append(company_info)

                    # 添加到天使投资公司列表
                    angel_investor = {
                        'id': node.get('@id', ''),
                        'name': investor_name,
                        'invested_companies': invested_companies
                    }
                    angel_investors.append(angel_investor)

    return angel_investors

def analyze_investment_rounds(angel_investors):
    """
    分析投资轮次分布

    Args:
        angel_investors: 天使投资公司列表

    Returns:
        投资轮次统计
    """
    round_stats = defaultdict(int)

    for investor in angel_investors:
        for company in investor['invested_companies']:
            round_type = company['round']
            round_stats[round_type] += 1

    return dict(round_stats)

def main():
    # 数据文件路径
    data_file = os.path.join('data', 'invest-on-invent-KG.json')

    print(f"正在解析数据文件: {data_file}")

    # 加载数据
    data = load_json_data(data_file)

    # 提取天使投资公司
    angel_investors = extract_angel_investors(data)

    # 输出结果
    print(f"\n共找到 {len(angel_investors)} 家投资公司")

    # 显示前10家投资公司
    print("\n前10家投资公司:")
    for i, investor in enumerate(angel_investors[:10]):
        print(f"{i+1}. {investor['name']} (ID: {investor['id']})")
        print(f"   投资的公司数量: {len(investor['invested_companies'])}")

        # 显示该投资者投资的前3家公司
        if investor['invested_companies']:
            print("   投资的部分公司:")
            for j, company in enumerate(investor['invested_companies'][:3]):
                print(f"     - 公司ID: {company['id']}, 轮次: {company['round']}, 日期: {company['date']}")
        print()

    # 分析投资轮次
    round_stats = analyze_investment_rounds(angel_investors)
    print("\n投资轮次分布:")
    for round_type, count in sorted(round_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {round_type}: {count}次投资")

    # 保存提取的数据
    output_file = 'models/angel_investors.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(angel_investors, f, ensure_ascii=False, indent=2)

    print(f"\n已将提取的天使投资公司数据保存到: {output_file}")

    return angel_investors

if __name__ == "__main__":
    main()
