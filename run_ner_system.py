#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time

def print_header(title):
    """打印带有分隔线的标题"""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, "="))
    print("=" * 80 + "\n")

def main():
    print_header("基于神经网络的实体识别系统")

    # 步骤1：训练和评估神经网络模型
    print_header("步骤1：训练和评估神经网络模型")
    print("正在训练和评估BiLSTM-CRF模型...")

    try:
        import src.train_ner_model as train_ner_model
        print("BiLSTM-CRF模型训练和评估完成")
    except Exception as e:
        print(f"训练和评估BiLSTM-CRF模型时出错: {e}")
        return

    # 步骤2：标注天使投资公司网页文本并进行实体识别
    print_header("步骤2：标注天使投资公司网页文本并进行实体识别")
    print("正在标注天使投资公司网页文本并进行实体识别...")

    try:
        import src.angel_investor_ner as angel_investor_ner
        print("天使投资公司网页文本标注和实体识别完成")
    except Exception as e:
        print(f"标注天使投资公司网页文本并进行实体识别时出错: {e}")
        return

    # 步骤3：分析模型可用性并提出优化方案
    print_header("步骤3：分析模型可用性并提出优化方案")
    print("正在分析模型可用性并提出优化方案...")

    try:
        import src.model_analysis as model_analysis
        print("模型可用性分析和优化方案提出完成")
    except Exception as e:
        print(f"分析模型可用性并提出优化方案时出错: {e}")
        return

    print_header("实体识别系统构建流程完成")
    print("已生成以下文件:")
    print("  - models/ner_model.h5: 训练好的BiLSTM-CRF模型")
    print("  - models/ner_vocab.json: 模型词汇表")
    print("  - models/training_history.png: 训练历史图表")
    print("  - models/angel_investor_ner_evaluation.json: 实体识别评估结果")
    print("  - models/model_usability_analysis.json: 模型可用性分析报告")
    print("  - models/entity_performance.png: 实体类型性能图表")

if __name__ == "__main__":
    main()
