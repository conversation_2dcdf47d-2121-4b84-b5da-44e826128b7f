#!/bin/bash

echo "Neo4j服务检查工具"
echo "================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python环境，请安装Python 3.6+"
    exit 1
fi

echo
echo "正在检查Neo4j服务..."
python3 src/check_neo4j_service.py

if [ $? -ne 0 ]; then
    echo
    echo "请先启动Neo4j服务，然后再尝试导入数据。"
else
    echo
    echo "Neo4j服务正在运行，可以继续导入数据。"
    echo "请运行 ./run_direct_import.sh 导入数据。"
fi

read -p "按任意键继续..." key
