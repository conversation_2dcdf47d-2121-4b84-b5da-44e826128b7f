import asyncio
import json
import random
import re
from urllib.parse import quote
from playwright.async_api import async_playwright
from typing import List, Dict, Optional, Any
from playwright_stealth import stealth_async


def read_company_names(file_path: str) -> List[str]:
    """读取JSON格式的公司名称文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # 处理两种JSON格式：纯列表或对象列表
            if isinstance(data, list) and len(data) > 0:
                if isinstance(data[0], str):
                    return data  # 直接是公司名称列表
                elif isinstance(data[0], dict) and "name" in data[0]:
                    return [item["name"] for item in data]  # 对象中的name字段
                else:
                    raise ValueError("JSON格式不符合预期，需要字符串列表或包含name字段的对象列表")
            else:
                raise ValueError("JSON格式应为非空列表")
    except Exception as e:
        print(f"读取公司名称文件出错：{e}")
        return []


async def human_delay(min_ms: int = 1000, max_ms: int = 3000) -> None:
    """模拟人类操作延时"""
    await asyncio.sleep(random.uniform(min_ms / 1000, max_ms / 1000))


async def fetch_baidu_info(page, company_name: str) -> Optional[Dict]:
    """从百度百科获取公司信息"""
    encoded_name = quote(company_name, safe='')
    url = f"https://baike.baidu.com/item/{encoded_name}"
    print(f"[百度百科] 正在请求: {url}")
    
    for retry in range(3):
        try:
            await page.goto(url, timeout=20000, wait_until="domcontentloaded")
            print(f"[百度百科] {company_name} 页面加载完成")
            
            # 检测是否跳转到验证页面
            if await page.query_selector('#verify_page'):
                print(f"[百度百科] 触发验证 {company_name} 第{retry + 1}次重试")
                await human_delay(3000, 5000)
                continue
                
            # 等待基础信息模块加载
            try:
                await page.wait_for_selector('.basicInfo_Dxt9K', state="attached", timeout=10000)
                print(f"[百度百科] {company_name} 基础信息模块加载完成")
            except:
                if await page.query_selector('.searchResult_ZtU9s'):
                    print(f"[百度百科] {company_name} 无对应词条")
                    return {"source": "baidu", "info": "无对应百科词条"}
                print(f"[百度百科] {company_name} 未找到基础信息模块")
                return None
                
            info_dict = {}
            
            # 模拟人类浏览行为
            for _ in range(3):
                await page.mouse.wheel(0, random.randint(200, 500))
                await human_delay(300, 800)
                
            # 提取基本信息
            blocks = await page.query_selector_all('.basicInfoBlock_zx4H_')
            if not blocks:
                print(f"[百度百科] {company_name} 无基础信息")
                return {"source": "baidu", "info": "无基础信息"}
                
            print(f"[百度百科] {company_name} 开始解析基本信息")
            for block in blocks:
                dt_elements = await block.query_selector_all('dt:not(.hide)')
                dd_elements = await block.query_selector_all('dd:not(.hide)')
                
                for dt, dd in zip(dt_elements, dd_elements):
                    key = await dt.inner_text()
                    value = await dd.inner_text()
                    info_dict[key.strip(' \n\u2002')] = value.replace('\n', ' ').strip()
            
            print(f"[百度百科] {company_name} 信息提取完成")
            return {
                "source": "baidu",
                "company_name": company_name,
                "info": info_dict
            }
            
        except Exception as e:
            print(f"[百度百科] {company_name} 第{retry + 1}次尝试失败：{str(e)[:100]}")
            await human_delay(2000, 5000)
            if retry == 2:
                print(f"[百度百科] {company_name} 达到最大重试次数")
                return None


async def fetch_wikipedia_info(page, company_name: str) -> Optional[Dict]:
    """从维基百科获取公司信息"""
    encoded_name = quote(company_name, safe='')
    url = f"https://zh.wikipedia.org/wiki/{encoded_name}"
    print(f"[维基百科] 正在请求: {url}")
    
    for retry in range(3):
        try:
            await page.goto(url, timeout=20000, wait_until="domcontentloaded")
            print(f"[维基百科] {company_name} 页面加载完成")
            
            # 检测是否为搜索结果页
            if await page.query_selector('#noarticletext'):
                print(f"[维基百科] {company_name} 无对应词条")
                return {"source": "wikipedia", "info": "无对应百科词条"}
                
            # 等待信息框加载
            try:
                await page.wait_for_selector('.infobox', state="attached", timeout=10000)
                print(f"[维基百科] {company_name} 信息框加载完成")
            except:
                print(f"[维基百科] {company_name} 未找到信息框")
                return {"source": "wikipedia", "info": "无基础信息"}
                
            info_dict = {}
            
            # 模拟人类浏览
            for _ in range(2):
                await page.mouse.wheel(0, random.randint(300, 600))
                await human_delay(400, 900)
                
            # 提取信息框内容
            infobox = await page.query_selector('.infobox')
            if not infobox:
                print(f"[维基百科] {company_name} 无基础信息")
                return {"source": "wikipedia", "info": "无基础信息"}
                
            print(f"[维基百科] {company_name} 开始解析信息框")
            rows = await infobox.query_selector_all('tr')
            for row in rows:
                th = await row.query_selector('th')
                td = await row.query_selector('td')
                if th and td:
                    key = await th.inner_text()
                    value = await td.inner_text()
                    info_dict[key.strip()] = value.replace('\n', ' ').strip()
            
            print(f"[维基百科] {company_name} 信息提取完成")
            return {
                "source": "wikipedia",
                "company_name": company_name,
                "info": info_dict
            }
            
        except Exception as e:
            print(f"[维基百科] {company_name} 第{retry + 1}次尝试失败：{str(e)[:100]}")
            await human_delay(2000, 5000)
            if retry == 2:
                print(f"[维基百科] {company_name} 达到最大重试次数")
                return None


async def fetch_so_info(page, company_name: str) -> Optional[Dict]:
    """从360百科获取公司信息"""
    encoded_name = quote(company_name, safe='')
    url = f"https://baike.so.com/doc/{encoded_name}"
    print(f"[360百科] 正在请求: {url}")
    
    for retry in range(3):
        try:
            await page.goto(url, timeout=20000, wait_until="domcontentloaded")
            print(f"[360百科] {company_name} 页面加载完成")
            
            # 检测是否为搜索结果页
            if await page.query_selector('.search-result'):
                print(f"[360百科] {company_name} 无对应词条")
                return {"source": "so", "info": "无对应百科词条"}
                
            # 等待信息模块加载
            try:
                await page.wait_for_selector('.basic-info', state="attached", timeout=10000)
                print(f"[360百科] {company_name} 基本信息模块加载完成")
            except:
                print(f"[360百科] {company_name} 未找到基本信息模块")
                return {"source": "so", "info": "无基础信息"}
                
            info_dict = {}
            
            # 模拟浏览行为
            for _ in range(2):
                await page.mouse.wheel(0, random.randint(200, 500))
                await human_delay(300, 700)
                
            # 提取基本信息
            basic_info = await page.query_selector('.basic-info')
            if not basic_info:
                print(f"[360百科] {company_name} 无基础信息")
                return {"source": "so", "info": "无基础信息"}
                
            print(f"[360百科] {company_name} 开始解析基本信息")
            name_elements = await basic_info.query_selector_all('.name')
            value_elements = await basic_info.query_selector_all('.value')
            
            for name, value in zip(name_elements, value_elements):
                key = await name.inner_text()
                value_text = await value.inner_text()
                info_dict[key.strip()] = value_text.replace('\n', ' ').strip()
            
            print(f"[360百科] {company_name} 信息提取完成")
            return {
                "source": "so",
                "company_name": company_name,
                "info": info_dict
            }
            
        except Exception as e:
            print(f"[360百科] {company_name} 第{retry + 1}次尝试失败：{str(e)[:100]}")
            await human_delay(2000, 5000)
            if retry == 2:
                print(f"[360百科] {company_name} 达到最大重试次数")
                return None


async def worker(browser, queue: asyncio.Queue, results: list) -> None:
    """工作协程，处理队列中的公司名称并从多个百科源获取信息"""
    context = await browser.new_context(
        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
        viewport={"width": 1920, "height": 1080},
        locale="zh-CN",
        timezone_id="Asia/Shanghai",
        extra_http_headers={
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://www.baidu.com/',
        },
    )
    
    # 禁用非必要资源加载
    await context.route(re.compile(r".(png|jpg|jpeg|gif|css|woff|woff2|svg|ico)$"), lambda route: route.abort())
    
    page = await context.new_page()
    await stealth_async(page)
    
    try:
        while not queue.empty():
            company_name = await queue.get()
            print(f"\n[开始处理] {company_name}")
            company_results = {"company_name": company_name, "sources": {}}
            success = False
            
            try:
                # 按顺序尝试各个百科源，直到找到有效信息
                sources = [
                    ("baidu", fetch_baidu_info),
                    ("wikipedia", fetch_wikipedia_info),
                    ("so", fetch_so_info)
                ]
                
                for source_name, fetch_func in sources:
                    print(f"[尝试来源] {source_name}")
                    await human_delay(800, 2500)  # 不同源之间的请求间隔
                    result = await fetch_func(page, company_name)
                    
                    if result and result.get("info"):
                        if result["info"] == "无对应百科词条":
                            print(f"[{source_name}] {company_name} 无对应词条")
                        elif result["info"] == "无基础信息":
                            print(f"[{source_name}] {company_name} 无基础信息")
                        else:
                            company_results["sources"][source_name] = result["info"]
                            print(f"[成功] 从{source_name}获取到 {company_name} 的有效信息")
                            success = True
                            break  # 只要获取到一个有效结果就停止尝试其他源
                    else:
                        print(f"[{source_name}] {company_name} 获取失败")
                
                if success:
                    results.append(company_results)
                else:
                    print(f"[失败] 未能获取 {company_name} 的有效信息")
                    
            finally:
                queue.task_done()
    finally:
        await context.close()


async def main_crawl(company_names: List[str], concurrency: int = 3) -> Dict[str, Any]:
    """主爬取函数"""
    print(f"开始爬取 {len(company_names)} 家公司信息，并发数: {concurrency}")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=True,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--disable-gpu',
            ],
            channel="chrome",
        )
        
        queue = asyncio.Queue()
        for name in company_names:
            await queue.put(name)
            
        results = []
        workers_list = [worker(browser, queue, results) for _ in range(concurrency)]
        await asyncio.gather(*workers_list, return_exceptions=True)
        
        # 保存结果（去除外层中括号）
        with open('models/angel_investors_with_multi_encyclopedia.json', 'w', encoding='utf-8') as f:
            for item in results:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")  # 每行一个JSON对象
            
        
        await browser.close()
        
        return {
            "results": results,
            "total": len(company_names)
        }


if __name__ == "__main__":
    # 读取所有公司名称（JSON格式）
    companies = read_company_names("models/angel_investors.json")
    if not companies:
        print("未读取到有效公司名称")
    else:
        print(f"准备爬取 {len(companies)} 家公司信息")
        asyncio.run(main_crawl(companies, concurrency=3))
