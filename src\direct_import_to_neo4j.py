#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import csv
import argparse
import time
import logging
from pathlib import Path
from py2neo import Graph, Node, Relationship, NodeMatcher, Transaction

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('neo4j_import.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def connect_to_neo4j(uri="bolt://localhost:7687", user="neo4j", password="password"):
    """
    连接到Neo4j数据库
    
    Args:
        uri: Neo4j数据库URI
        user: 用户名
        password: 密码
        
    Returns:
        Graph对象或None
    """
    logger.info(f"连接到Neo4j数据库: {uri}")
    try:
        graph = Graph(uri, auth=(user, password))
        logger.info("连接成功")
        return graph
    except Exception as e:
        logger.error(f"连接Neo4j数据库失败: {e}")
        return None

def clear_database(graph):
    """
    清空数据库中的所有数据
    
    Args:
        graph: Graph对象
        
    Returns:
        是否成功
    """
    logger.info("清空数据库")
    try:
        graph.run("MATCH (n) DETACH DELETE n")
        logger.info("数据库已清空")
        return True
    except Exception as e:
        logger.error(f"清空数据库失败: {e}")
        return False

def read_csv_file(file_path):
    """
    读取CSV文件
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        包含CSV数据的列表，每个元素是一个字典
    """
    logger.info(f"读取CSV文件: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            data = [row for row in reader]
        logger.info(f"读取了 {len(data)} 行数据")
        return data
    except Exception as e:
        logger.error(f"读取CSV文件失败: {e}")
        return []

def import_nodes(graph, label, csv_file):
    """
    导入节点数据
    
    Args:
        graph: Graph对象
        label: 节点标签
        csv_file: CSV文件路径
        
    Returns:
        导入的节点数量
    """
    logger.info(f"导入 {label} 节点数据")
    
    # 读取CSV文件
    data = read_csv_file(csv_file)
    if not data:
        return 0
    
    # 创建节点
    count = 0
    tx = graph.begin()
    
    try:
        for row in data:
            # 提取ID
            node_id = row.pop('id:ID', None)
            if not node_id:
                logger.warning(f"跳过没有ID的行: {row}")
                continue
            
            # 提取标签
            node_labels = row.pop(':LABEL', label).split(';')
            
            # 创建节点
            properties = {k: v for k, v in row.items() if v}  # 过滤空值
            node = Node(*node_labels, **properties)
            node.__primarykey__ = "id"
            node.id = node_id
            
            # 添加到事务
            tx.create(node)
            count += 1
            
            # 每1000个节点提交一次事务
            if count % 1000 == 0:
                tx.commit()
                logger.info(f"已导入 {count} 个节点")
                tx = graph.begin()
        
        # 提交剩余的事务
        tx.commit()
        logger.info(f"共导入 {count} 个 {label} 节点")
        return count
    
    except Exception as e:
        logger.error(f"导入节点数据失败: {e}")
        tx.rollback()
        return 0

def import_relationships(graph, rel_type, csv_file):
    """
    导入关系数据
    
    Args:
        graph: Graph对象
        rel_type: 关系类型
        csv_file: CSV文件路径
        
    Returns:
        导入的关系数量
    """
    logger.info(f"导入 {rel_type} 关系数据")
    
    # 读取CSV文件
    data = read_csv_file(csv_file)
    if not data:
        return 0
    
    # 创建关系
    count = 0
    tx = graph.begin()
    matcher = NodeMatcher(graph)
    
    try:
        for row in data:
            # 提取起始节点和结束节点的ID
            start_id = row.pop(':START_ID', None)
            end_id = row.pop(':END_ID', None)
            
            if not start_id or not end_id:
                logger.warning(f"跳过没有起始节点或结束节点ID的行: {row}")
                continue
            
            # 提取关系类型
            relationship_type = row.pop(':TYPE', rel_type)
            
            # 查找起始节点和结束节点
            start_node = matcher.match().where(id=start_id).first()
            end_node = matcher.match().where(id=end_id).first()
            
            if not start_node or not end_node:
                logger.warning(f"找不到节点: start_id={start_id}, end_id={end_id}")
                continue
            
            # 创建关系
            properties = {k: v for k, v in row.items() if v}  # 过滤空值
            relationship = Relationship(start_node, relationship_type, end_node, **properties)
            
            # 添加到事务
            tx.create(relationship)
            count += 1
            
            # 每1000个关系提交一次事务
            if count % 1000 == 0:
                tx.commit()
                logger.info(f"已导入 {count} 个关系")
                tx = graph.begin()
        
        # 提交剩余的事务
        tx.commit()
        logger.info(f"共导入 {count} 个 {rel_type} 关系")
        return count
    
    except Exception as e:
        logger.error(f"导入关系数据失败: {e}")
        tx.rollback()
        return 0

def import_all_data(graph, csv_dir="neo4j_import"):
    """
    导入所有数据
    
    Args:
        graph: Graph对象
        csv_dir: CSV文件目录
        
    Returns:
        是否成功
    """
    logger.info(f"开始导入所有数据，CSV文件目录: {csv_dir}")
    
    # 检查CSV文件目录是否存在
    if not os.path.exists(csv_dir):
        logger.error(f"CSV文件目录不存在: {csv_dir}")
        return False
    
    # 导入节点
    node_files = {
        "AngelInvestor": os.path.join(csv_dir, "angel_investor.csv"),
        "VentureCapital": os.path.join(csv_dir, "venture_capital.csv"),
        "StartupCompany": os.path.join(csv_dir, "company.csv"),
        "InvestmentRound": os.path.join(csv_dir, "investment_round.csv"),
        "InvestmentEvent": os.path.join(csv_dir, "investment_event.csv")
    }
    
    total_nodes = 0
    for label, file_path in node_files.items():
        if os.path.exists(file_path):
            count = import_nodes(graph, label, file_path)
            total_nodes += count
        else:
            logger.warning(f"节点文件不存在: {file_path}")
    
    # 导入关系
    relationship_files = {
        "INVESTS": os.path.join(csv_dir, "invests_relationship.csv"),
        "HAS_INVESTMENT_EVENT": os.path.join(csv_dir, "has_event_relationship.csv"),
        "HAS_ROUND": os.path.join(csv_dir, "has_round_relationship.csv")
    }
    
    total_relationships = 0
    for rel_type, file_path in relationship_files.items():
        if os.path.exists(file_path):
            count = import_relationships(graph, rel_type, file_path)
            total_relationships += count
        else:
            logger.warning(f"关系文件不存在: {file_path}")
    
    logger.info(f"导入完成，共导入 {total_nodes} 个节点和 {total_relationships} 个关系")
    return True

def create_indexes(graph):
    """
    创建索引
    
    Args:
        graph: Graph对象
        
    Returns:
        是否成功
    """
    logger.info("创建索引")
    try:
        # 为每种节点类型创建索引
        graph.run("CREATE INDEX ON :AngelInvestor(id)")
        graph.run("CREATE INDEX ON :VentureCapital(id)")
        graph.run("CREATE INDEX ON :StartupCompany(id)")
        graph.run("CREATE INDEX ON :InvestmentRound(id)")
        graph.run("CREATE INDEX ON :InvestmentEvent(id)")
        
        # 为常用属性创建索引
        graph.run("CREATE INDEX ON :AngelInvestor(name)")
        graph.run("CREATE INDEX ON :VentureCapital(name)")
        graph.run("CREATE INDEX ON :StartupCompany(name)")
        graph.run("CREATE INDEX ON :InvestmentRound(name)")
        
        logger.info("索引创建成功")
        return True
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='直接将CSV数据导入到Neo4j数据库')
    parser.add_argument('--uri', default='bolt://localhost:7687', help='Neo4j数据库URI')
    parser.add_argument('--user', default='neo4j', help='Neo4j用户名')
    parser.add_argument('--password', default='password', help='Neo4j密码')
    parser.add_argument('--csv-dir', default='neo4j_import', help='CSV文件目录')
    parser.add_argument('--clear', action='store_true', help='导入前清空数据库')
    parser.add_argument('--no-indexes', action='store_true', help='不创建索引')
    
    args = parser.parse_args()
    
    logger.info("开始导入数据到Neo4j")
    logger.info(f"Neo4j URI: {args.uri}")
    logger.info(f"CSV文件目录: {args.csv_dir}")
    
    # 连接到Neo4j数据库
    graph = connect_to_neo4j(args.uri, args.user, args.password)
    if not graph:
        logger.error("无法连接到Neo4j数据库，导入过程终止")
        return 1
    
    # 清空数据库
    if args.clear:
        if not clear_database(graph):
            logger.error("清空数据库失败，导入过程终止")
            return 1
    
    # 导入所有数据
    if not import_all_data(graph, args.csv_dir):
        logger.error("导入数据失败，导入过程终止")
        return 1
    
    # 创建索引
    if not args.no_indexes:
        create_indexes(graph)
    
    logger.info("导入过程完成")
    logger.info("请访问 http://localhost:7474 查看导入的数据")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
