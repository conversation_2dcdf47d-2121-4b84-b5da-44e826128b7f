@echo off
echo 启动Neo4j服务
echo ============

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 此脚本需要管理员权限才能启动Neo4j服务。
    echo 请右键点击此脚本，选择"以管理员身份运行"。
    pause
    exit /b 1
)

REM 尝试使用Windows服务启动Neo4j
echo 尝试启动Neo4j服务...
net start neo4j
if %errorlevel% neq 0 (
    echo 无法使用Windows服务启动Neo4j。
    
    REM 尝试使用Neo4j命令启动
    echo 尝试使用Neo4j命令启动...
    
    REM 检查常见的Neo4j安装路径
    set NEO4J_PATHS=^
    "C:\Program Files\Neo4j\bin\neo4j.bat" ^
    "C:\Program Files (x86)\Neo4j\bin\neo4j.bat" ^
    "C:\Neo4j\bin\neo4j.bat"
    
    set NEO4J_CMD=
    for %%p in (%NEO4J_PATHS%) do (
        if exist %%p (
            set NEO4J_CMD=%%p
            goto :found
        )
    )
    
    :found
    if defined NEO4J_CMD (
        echo 找到Neo4j命令: %NEO4J_CMD%
        call %NEO4J_CMD% start
    ) else (
        echo 无法找到Neo4j命令。
        echo 请手动启动Neo4j服务，或者指定Neo4j安装路径。
        
        set /p neo4j_path="请输入Neo4j安装路径 (例如 C:\Program Files\Neo4j): "
        if exist "%neo4j_path%\bin\neo4j.bat" (
            call "%neo4j_path%\bin\neo4j.bat" start
        ) else (
            echo 无法找到Neo4j命令: %neo4j_path%\bin\neo4j.bat
            echo 请手动启动Neo4j服务。
            pause
            exit /b 1
        )
    )
)

echo.
echo 正在检查Neo4j服务是否已启动...
timeout /t 5 /nobreak >nul

REM 检查Neo4j服务是否已启动
python src/check_neo4j_service.py
if %errorlevel% neq 0 (
    echo 警告: Neo4j服务可能未成功启动。
    echo 请手动检查Neo4j服务状态。
    pause
    exit /b 1
) else (
    echo Neo4j服务已成功启动。
    echo 现在可以运行 run_direct_import.bat 导入数据了。
)

pause
