@echo off
echo Neo4j服务检查工具
echo ================

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请安装Python 3.6+
    pause
    exit /b 1
)

echo.
echo 正在检查Neo4j服务...
python src/check_neo4j_service.py

if %errorlevel% neq 0 (
    echo.
    echo 请先启动Neo4j服务，然后再尝试导入数据。
) else (
    echo.
    echo Neo4j服务正在运行，可以继续导入数据。
    echo 请运行 run_direct_import.bat 导入数据。
)

pause
