#!/bin/bash

echo "启动Neo4j服务"
echo "============"

# 检查是否以root用户运行
if [ "$EUID" -ne 0 ]; then
    echo "警告: 此脚本可能需要root权限才能启动Neo4j服务。"
    echo "请使用sudo运行此脚本。"
    read -p "是否继续? (y/n): " continue
    if [ "$continue" != "y" ]; then
        echo "已取消。"
        exit 1
    fi
fi

# 尝试使用systemd启动Neo4j
echo "尝试使用systemd启动Neo4j服务..."
systemctl start neo4j 2>/dev/null
if [ $? -eq 0 ]; then
    echo "Neo4j服务已启动。"
else
    echo "无法使用systemd启动Neo4j。"
    
    # 尝试使用Neo4j命令启动
    echo "尝试使用Neo4j命令启动..."
    
    # 检查常见的Neo4j安装路径
    NEO4J_PATHS=(
        "/usr/bin/neo4j"
        "/usr/local/bin/neo4j"
        "/opt/neo4j/bin/neo4j"
    )
    
    NEO4J_CMD=""
    for path in "${NEO4J_PATHS[@]}"; do
        if [ -f "$path" ]; then
            NEO4J_CMD="$path"
            break
        fi
    done
    
    if [ -n "$NEO4J_CMD" ]; then
        echo "找到Neo4j命令: $NEO4J_CMD"
        $NEO4J_CMD start
    else
        echo "无法找到Neo4j命令。"
        echo "请手动启动Neo4j服务，或者指定Neo4j安装路径。"
        
        read -p "请输入Neo4j安装路径 (例如 /opt/neo4j): " neo4j_path
        if [ -f "$neo4j_path/bin/neo4j" ]; then
            "$neo4j_path/bin/neo4j" start
        else
            echo "无法找到Neo4j命令: $neo4j_path/bin/neo4j"
            echo "请手动启动Neo4j服务。"
            exit 1
        fi
    fi
fi

echo
echo "正在检查Neo4j服务是否已启动..."
sleep 5

# 检查Neo4j服务是否已启动
python3 src/check_neo4j_service.py
if [ $? -ne 0 ]; then
    echo "警告: Neo4j服务可能未成功启动。"
    echo "请手动检查Neo4j服务状态。"
    exit 1
else
    echo "Neo4j服务已成功启动。"
    echo "现在可以运行 ./run_direct_import.sh 导入数据了。"
fi

read -p "按任意键继续..." key
