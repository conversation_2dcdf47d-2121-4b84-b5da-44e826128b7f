# 中国天使投资知识图谱

## 项目概述

本项目构建了一个中国天使投资知识图谱，旨在为天使投资智能问答系统提供数据基础。项目包含数据爬取、知识图谱构建、可视化展示等功能。

## 主要特性

### 🎯 **增强版知识图谱**
- **丰富的节点类型**: 投资机构、公司、人员、行业、地点、公司类型、成立日期等
- **多样化关系**: 创立关系、地理关系、行业关系、投资关系等
- **详细属性信息**: 每个节点都包含完整的属性信息

### 🎨 **可视化效果**
- **彩色节点**: 不同类型的节点使用不同颜色区分
- **交互式探索**: 点击节点可查看相关联的详细信息节点
- **关系网络**: 通过关系连接展示实体间的复杂联系

### 📊 **数据统计**
- **投资机构**: 11,200+ 家（天使投资机构 + 风险投资机构）
- **创业公司**: 10,000+ 家
- **人员信息**: 创始人、法定代表人等
- **地理分布**: 全国各地投资机构分布
- **行业覆盖**: 科技、医疗、金融等多个行业

## 项目结构

```
KGtianshi/
├── src/                                    # 源代码目录
│   ├── parse_investor_data_enhanced.py     # 增强版数据解析脚本
│   ├── import_enhanced_to_neo4j.py         # 增强版Neo4j导入脚本
│   ├── query_examples.py                   # 查询示例脚本
│   ├── apply_neo4j_style.py               # 样式应用脚本
│   └── baidu_baike_crawler.py              # 百度百科爬虫
├── models/                                 # 模型和数据文件
│   └── angel_investors_processed.json      # 处理后的投资者数据
├── data/                                   # 原始数据目录
│   ├── invest-on-invent-KG.json           # 投资关系数据
│   └── extracted/                          # 提取的数据
├── neo4j_import_enhanced/                  # 增强版CSV文件
│   ├── investors.csv                       # 投资机构节点
│   ├── companies.csv                       # 公司节点
│   ├── persons.csv                         # 人员节点
│   ├── industries.csv                      # 行业节点
│   ├── locations.csv                       # 地点节点
│   ├── company_types.csv                   # 公司类型节点
│   ├── founding_dates.csv                  # 成立日期节点
│   └── *_relationships.csv                 # 各种关系文件
├── docs/                                   # 文档目录
├── neo4j_style.grass                       # Neo4j样式配置
├── Neo4j_使用说明.md                       # 使用说明文档
└── main.py                                 # 主程序入口
```

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install py2neo requests beautifulsoup4 lxml

# 启动Neo4j数据库
# 确保Neo4j运行在 localhost:7687
# 用户名: neo4j, 密码: angelinvestment
```

### 2. 生成增强版CSV文件

```bash
python main.py --action parse
```

### 3. 导入数据到Neo4j

```bash
python main.py --action import
```

### 4. 应用样式配置

```bash
python src/apply_neo4j_style.py --database=""
```

### 5. 查看知识图谱

打开 http://localhost:7474 访问Neo4j Browser

## 节点类型和关系

### 节点类型
- **Investor**: 投资机构（天使投资机构/风险投资机构）
- **Company**: 创业公司
- **Person**: 人员（创始人、法定代表人）
- **Industry**: 行业
- **Location**: 地点
- **CompanyType**: 公司类型
- **FoundingDate**: 成立日期
- **InvestmentRound**: 投资轮次
- **InvestmentEvent**: 投资事件

### 关系类型
- **FOUNDED_BY**: 创立关系
- **LEGAL_REPRESENTATIVE**: 法定代表人关系
- **LOCATED_IN**: 地理位置关系
- **BELONGS_TO_INDUSTRY**: 行业归属关系
- **HAS_TYPE**: 类型关系
- **FOUNDED_ON**: 成立日期关系
- **INVESTS**: 投资关系
- **HAS_INVESTMENT_EVENT**: 投资事件关系
- **HAS_ROUND**: 轮次关系

## 样式配置

知识图谱使用不同颜色区分节点类型：

- **天使投资机构**: 红色 (#FF6B6B)
- **风险投资机构**: 青色 (#4ECDC4)
- **创业公司**: 蓝色 (#45B7D1)
- **人员**: 绿色 (#96CEB4)
- **行业**: 橙色 (#FDCB6E)
- **地点**: 紫色 (#A29BFE)
- **公司类型**: 粉色 (#FD79A8)
- **成立日期**: 黄色 (#FFEAA7)

## 常用查询

### 查看投资机构及其相关信息
```cypher
MATCH (investor:Investor)-[r]-(related)
WHERE investor.name CONTAINS "华控"
RETURN investor, r, related
LIMIT 50
```

### 查看某个行业的投资分布
```cypher
MATCH (industry:Industry)<-[:BELONGS_TO_INDUSTRY]-(investor:Investor)
WHERE industry.name = "科技"
RETURN industry, investor
LIMIT 25
```

### 查看地区投资机构分布
```cypher
MATCH (location:Location)<-[:LOCATED_IN]-(investor:Investor)
RETURN location.name as 地区, count(investor) as 投资机构数量
ORDER BY 投资机构数量 DESC
LIMIT 10
```

### 查看创始人网络
```cypher
MATCH (person:Person)-[:FOUNDED_BY]-(investor:Investor)
RETURN person, investor
LIMIT 25
```

## 数据来源

- **投资机构数据**: 来自公开的投资机构名录
- **百度百科数据**: 通过爬虫获取的详细信息
- **投资关系数据**: 来自公开的投资数据库

## 技术栈

- **数据库**: Neo4j 图数据库
- **编程语言**: Python 3.8+
- **主要库**: py2neo, requests, beautifulsoup4
- **可视化**: Neo4j Browser

## 贡献指南

1. Fork 本项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请提交 Issue 或联系项目维护者。
