# 中国天使投资知识图谱与实体识别系统

本项目包含两个主要部分：
1. 中国天使投资知识图谱构建
2. 基于BiLSTM-CRF的实体识别系统

## 项目结构

```
KGtianshi/
├── data/                       # 数据目录
│   ├── angel_investor/         # 天使投资公司网页文本和标注
│   ├── ner/                    # 命名实体识别训练数据
│   └── invest-on-invent-KG.json # 天使投资公司数据源
├── models/                     # 模型和评估结果目录
│   ├── ner_model.h5            # 训练好的BiLSTM-CRF模型
│   ├── ner_vocab.json          # 模型词汇表
│   ├── training_history.png    # 训练历史图表
│   ├── angel_investor_ner_evaluation.json # 实体识别评估结果
│   ├── model_usability_analysis.json # 模型可用性分析报告
│   └── entity_performance.png  # 实体类型性能图表
├── src/                        # 源代码目录
│   ├── bilstm_crf_ner.py       # BiLSTM-CRF模型实现
│   ├── train_ner_model.py      # 训练和评估神经网络模型
│   ├── angel_investor_ner.py   # 天使投资公司网页文本标注和实体识别
│   └── model_analysis.py       # 模型可用性分析和优化方案
├── main.py                     # 知识图谱构建主程序
├── run_ner_system.py           # 实体识别系统主程序
├── README.md                   # 项目说明文件
└── README_NER.md               # 实体识别系统详细说明
```

## 第一部分：中国天使投资知识图谱构建

### 功能

1. 解析天使投资公司数据源（invest-on-invent-KG.json）
2. 通过百度百科检索和解析投资公司的相关信息
3. 构建中国天使投资知识图谱

### 使用方法

运行主程序：

```bash
python main.py
```

### 输出文件

- `angel_investors.json`: 解析的天使投资公司数据
- `angel_investors_with_baike.json`: 带有百度百科信息的天使投资公司数据
- `angel_investment_knowledge_graph.json`: 知识图谱数据
- `baidu_baike_data/`: 每家公司的百度百科数据

## 第二部分：基于BiLSTM-CRF的实体识别系统

### 功能

1. 训练和评估BiLSTM-CRF模型
2. 标注天使投资公司网页文本并进行实体识别
3. 分析模型可用性并提出优化方案

### 使用方法

运行主程序：

```bash
python run_ner_system.py
```

或者分步运行：

```bash
# 步骤1：训练和评估神经网络模型
python -m src.train_ner_model

# 步骤2：标注天使投资公司网页文本并进行实体识别
python -m src.angel_investor_ner

# 步骤3：分析模型可用性并提出优化方案
python -m src.model_analysis
```

### 输出文件

- `models/ner_model.h5`: 训练好的BiLSTM-CRF模型
- `models/ner_vocab.json`: 模型词汇表
- `models/training_history.png`: 训练历史图表
- `models/angel_investor_ner_evaluation.json`: 实体识别评估结果
- `models/model_usability_analysis.json`: 模型可用性分析报告
- `models/entity_performance.png`: 实体类型性能图表

## 依赖库

运行本项目需要安装以下Python库：

```bash
pip install requests beautifulsoup4 tensorflow tensorflow-addons numpy matplotlib scikit-learn seqeval
```

## BiLSTM-CRF模型原理

BiLSTM-CRF（双向长短期记忆网络-条件随机场）是一种用于序列标注任务的神经网络模型，特别适用于命名实体识别（NER）任务。它由两个主要部分组成：

### 1. 双向LSTM（BiLSTM）

- **LSTM（长短期记忆网络）**：是一种特殊的循环神经网络（RNN），能够学习长距离依赖关系，解决了传统RNN的梯度消失问题。
- **双向（Bidirectional）**：同时从前向和后向两个方向处理序列，捕获更全面的上下文信息。
- **作用**：BiLSTM层能够为序列中的每个词生成一个特征向量，这个向量包含了该词的上下文信息。

### 2. 条件随机场（CRF）

- **CRF**：是一种概率图模型，用于对序列数据进行标注。
- **作用**：CRF层考虑了标签之间的依赖关系，确保预测的标签序列是合法的（例如，I-ORG标签不会出现在O标签之后，而应该跟在B-ORG之后）。

### 工作流程

1. **输入层**：接收词语的索引序列。
2. **嵌入层**：将词语索引转换为密集向量表示。
3. **BiLSTM层**：从前向和后向两个方向处理序列，生成上下文感知的特征表示。
4. **全连接层**：将BiLSTM的输出映射到标签空间。
5. **CRF层**：考虑标签之间的依赖关系，输出最优的标签序列。

## 实体类型

本项目识别以下类型的实体：

- **ORG**：组织机构，如"红杉资本"、"IDG资本"等
- **PER**：人物，如"张磊"、"徐小平"等
- **LOC**：地点，如"北京"、"上海"等
- **TIME**：时间，如"2005年"、"1992年"等

## 更多详细信息

有关实体识别系统的更多详细信息，请参阅 [README_NER.md](README_NER.md)。
