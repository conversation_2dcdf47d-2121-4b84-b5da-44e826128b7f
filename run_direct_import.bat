@echo off
echo 中国天使投资知识图谱 - Neo4j直接导入工具
echo ======================================

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请安装Python 3.6+
    pause
    exit /b 1
)

REM 检查Neo4j服务是否运行
echo 正在检查Neo4j服务...
python src/check_neo4j_service.py >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: Neo4j服务可能未运行。
    echo 请确保Neo4j服务已启动，否则导入可能会失败。
    set /p continue="是否继续? (y/n): "
    if /i not "%continue%"=="y" (
        echo 导入已取消。
        pause
        exit /b 1
    )
) else (
    echo Neo4j服务正在运行。
)

REM 检查py2neo库
python -c "import py2neo" >nul 2>&1
if %errorlevel% neq 0 (
    echo 正在安装py2neo库...
    pip install py2neo
    if %errorlevel% neq 0 (
        echo 安装py2neo失败，请手动安装: pip install py2neo
        pause
        exit /b 1
    )
)

echo.
echo 请输入Neo4j数据库连接信息:
set /p uri="Neo4j URI [bolt://localhost:7687]: "
set /p user="用户名 [neo4j]: "
set /p password="密码 [password]: "

REM 设置默认值
if "%uri%"=="" set uri=bolt://localhost:7687
if "%user%"=="" set user=neo4j
if "%password%"=="" set password=password

echo.
echo 请选择导入选项:
echo 1. 导入前清空数据库
echo 2. 保留现有数据
set /p clear_option="请输入选项 (1-2): "

set /p database="数据库名称 [angelinvestment]: "
if "%database%"=="" set database=angelinvestment

if "%clear_option%"=="1" (
    echo 执行导入（清空数据库）...
    python src/direct_import_to_neo4j.py --uri="%uri%" --user="%user%" --password="%password%" --database="%database%" --clear
) else (
    echo 执行导入（保留现有数据）...
    python src/direct_import_to_neo4j.py --uri="%uri%" --user="%user%" --password="%password%" --database="%database%"
)

if %errorlevel% neq 0 (
    echo 导入过程中出现错误，请查看日志文件 neo4j_import.log
) else (
    echo.
    echo 导入过程完成!
    echo 详细日志保存在 neo4j_import.log
)

pause
