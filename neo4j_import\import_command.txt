
# Neo4j批量导入命令
# 请根据您的Neo4j安装路径和数据库名称进行调整

# Windows系统
neo4j-admin import --database=angelinvestment --nodes=AngelInvestor=neo4j_import/angel_investor.csv --nodes=VentureCapital=neo4j_import/venture_capital.csv --nodes=StartupCompany=neo4j_import/company.csv --nodes=InvestmentRound=neo4j_import/investment_round.csv --nodes=InvestmentEvent=neo4j_import/investment_event.csv --relationships=INVESTS=neo4j_import/invests_relationship.csv --relationships=HAS_INVESTMENT_EVENT=neo4j_import/has_event_relationship.csv --relationships=HAS_ROUND=neo4j_import/has_round_relationship.csv --delimiter="," --array-delimiter=";" --id-type=STRING

# Linux/Mac系统
# ./bin/neo4j-admin import --database=angelinvestment --nodes=AngelInvestor=neo4j_import/angel_investor.csv --nodes=VentureCapital=neo4j_import/venture_capital.csv --nodes=StartupCompany=neo4j_import/company.csv --nodes=InvestmentRound=neo4j_import/investment_round.csv --nodes=InvestmentEvent=neo4j_import/investment_event.csv --relationships=INVESTS=neo4j_import/invests_relationship.csv --relationships=HAS_INVESTMENT_EVENT=neo4j_import/has_event_relationship.csv --relationships=HAS_ROUND=neo4j_import/has_round_relationship.csv --delimiter="," --array-delimiter=";" --id-type=STRING

# 注意：
# 1. 执行导入命令前，请确保Neo4j服务已停止
# 2. 如果数据库已存在，请先删除或备份
# 3. 导入完成后，启动Neo4j服务
# 4. 导入命令需要根据实际环境进行调整
