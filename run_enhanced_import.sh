#!/bin/bash

echo "中国天使投资知识图谱 - 增强版数据导入"
echo "====================================="

echo ""
echo "步骤1: 生成增强版CSV文件..."
python src/parse_investor_data_enhanced.py
if [ $? -ne 0 ]; then
    echo "错误: CSV文件生成失败"
    exit 1
fi

echo ""
echo "步骤2: 导入数据到Neo4j..."
python src/import_enhanced_to_neo4j.py --database=""
if [ $? -ne 0 ]; then
    echo "错误: 数据导入失败"
    exit 1
fi

echo ""
echo "步骤3: 应用样式配置..."
python src/apply_neo4j_style.py --database=""
if [ $? -ne 0 ]; then
    echo "警告: 样式配置失败，但数据已成功导入"
fi

echo ""
echo "====================================="
echo "导入完成！"
echo "请访问 http://localhost:7474 查看知识图谱"
echo "====================================="
