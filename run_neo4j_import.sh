#!/bin/bash

echo "中国天使投资知识图谱 - Neo4j数据导入工具"
echo "======================================"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python环境，请安装Python 3.6+"
    exit 1
fi

echo
echo "请选择导入选项:"
echo "1. 完整导入流程（转换数据、导入Neo4j、更新配置、重启服务）"
echo "2. 仅转换数据和导入Neo4j（不更新配置和重启服务）"
echo "3. 仅导入Neo4j（假设CSV文件已存在）"
echo "4. 自定义Neo4j安装路径"
echo "5. 退出"

read -p "请输入选项 (1-5): " option

case $option in
    1)
        echo "执行完整导入流程..."
        python3 src/import_to_neo4j.py
        ;;
    2)
        echo "执行数据转换和导入..."
        python3 src/import_to_neo4j.py --skip-config --skip-restart
        ;;
    3)
        echo "仅执行Neo4j导入..."
        python3 src/import_to_neo4j.py --skip-convert --skip-config --skip-restart
        ;;
    4)
        read -p "请输入Neo4j安装路径: " neo4j_home
        echo "使用自定义Neo4j路径执行导入..."
        python3 src/import_to_neo4j.py --neo4j-home="$neo4j_home"
        ;;
    5)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo "无效的选项!"
        exit 1
        ;;
esac

if [ $? -ne 0 ]; then
    echo "导入过程中出现错误，请查看日志文件 neo4j_import.log"
else
    echo
    echo "导入过程完成!"
    echo "详细日志保存在 neo4j_import.log"
fi

read -p "按任意键继续..." key
