@echo off
echo 中国天使投资知识图谱 - Neo4j数据导入工具
echo ======================================

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请安装Python 3.6+
    pause
    exit /b 1
)

echo.
echo 请选择导入选项:
echo 1. 完整导入流程（转换数据、导入Neo4j、更新配置、重启服务）
echo 2. 仅转换数据和导入Neo4j（不更新配置和重启服务）
echo 3. 仅导入Neo4j（假设CSV文件已存在）
echo 4. 自定义Neo4j安装路径
echo 5. 退出

set /p option="请输入选项 (1-5): "

if "%option%"=="1" (
    echo 执行完整导入流程...
    python src/import_to_neo4j.py
) else if "%option%"=="2" (
    echo 执行数据转换和导入...
    python src/import_to_neo4j.py --skip-config --skip-restart
) else if "%option%"=="3" (
    echo 仅执行Neo4j导入...
    python src/import_to_neo4j.py --skip-convert --skip-config --skip-restart
) else if "%option%"=="4" (
    set /p neo4j_home="请输入Neo4j安装路径: "
    echo 使用自定义Neo4j路径执行导入...
    python src/import_to_neo4j.py --neo4j-home="%neo4j_home%"
) else if "%option%"=="5" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效的选项!
    pause
    exit /b 1
)

if %errorlevel% neq 0 (
    echo 导入过程中出现错误，请查看日志文件 neo4j_import.log
) else (
    echo.
    echo 导入过程完成!
    echo 详细日志保存在 neo4j_import.log
)

pause
