# 中国天使投资知识图谱

本项目通过解析天使投资公司数据源（invest-on-invent-KG.json），并结合百度百科的信息，构建中国天使投资知识图谱。

## 项目结构

- `parse_investor_data.py`: 解析天使投资公司数据源
- `baidu_baike_crawler.py`: 通过百度百科检索和解析投资公司信息
- `build_knowledge_graph.py`: 构建知识图谱
- `main.py`: 主程序，运行整个流程

## 依赖库

运行本项目需要安装以下Python库：

```bash
pip install requests beautifulsoup4
```

## 使用方法

1. 确保数据文件 `data/invest-on-invent-KG.json` 存在
2. 运行主程序：

```bash
python main.py
```

或者分步运行：

```bash
# 步骤1：解析天使投资公司数据源
python parse_investor_data.py

# 步骤2：通过百度百科检索和解析投资公司信息
python baidu_baike_crawler.py

# 步骤3：构建知识图谱
python build_knowledge_graph.py
```

## 输出文件

- `angel_investors.json`: 解析的天使投资公司数据
- `angel_investors_with_baike.json`: 带有百度百科信息的天使投资公司数据
- `angel_investment_knowledge_graph.json`: 知识图谱数据
- `baidu_baike_data/`: 每家公司的百度百科数据

## 知识图谱结构

知识图谱包含以下类型的节点：

- 投资公司（investor）：天使投资公司
- 被投资公司（company）：被投资的公司
- 投资领域（field）：投资公司关注的领域
- 人物（person）：创始人等

知识图谱包含以下类型的边：

- 投资（investor -> company）：投资关系，包含轮次和日期信息
- 关注领域（investor -> field）：投资公司关注的领域
- 创立（person -> investor）：创始人与投资公司的关系

## 核心功能说明

### 1. 解析天使投资公司数据源

`parse_investor_data.py` 脚本解析 `invest-on-invent-KG.json` 文件，提取天使投资公司信息，包括：

- 投资公司ID和名称
- 投资的公司列表
- 投资轮次和日期

### 2. 通过百度百科检索和解析投资公司信息

`baidu_baike_crawler.py` 脚本通过百度百科检索和解析投资公司的相关信息，包括：

- 成立时间
- 公司类型
- 总部地点
- 创始人
- 投资领域
- 经营范围
- 等其他信息

### 3. 构建知识图谱

`build_knowledge_graph.py` 脚本将解析的数据组织成知识图谱结构，包括：

- 构建节点（投资公司、被投资公司、投资领域、人物）
- 构建边（投资关系、关注领域、创立关系）
- 分析知识图谱（节点类型分布、边类型分布、投资轮次分布）
