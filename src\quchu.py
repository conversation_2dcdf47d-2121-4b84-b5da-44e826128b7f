import json
import re
import os

def remove_bracket_tags(json_data):
    """
    去除JSON数据中所有值里的中括号标签内容（如[31-32]、[2]等）
    
    Args:
        json_data: 解析后的JSON数据（列表或字典）
        
    Returns:
        处理后的JSON数据，中括号标签已被移除
    """
    # 处理列表类型数据
    if isinstance(json_data, list):
        return [remove_bracket_tags(item) for item in json_data]
    
    # 处理字典类型数据
    elif isinstance(json_data, dict):
        processed_dict = {}
        for key, value in json_data.items():
            # 递归处理字典的值
            processed_value = remove_bracket_tags(value)
            processed_dict[key] = processed_value
        return processed_dict
    
    # 处理字符串类型数据，去除中括号标签
    elif isinstance(json_data, str):
        # 使用正则表达式匹配并去除中括号及内部内容
        # 支持匹配 [数字]、[数字-数字]、[英文] 等形式
        return re.sub(r'\s*\[\d+(\-\d+)?\w*\]\s*', '', json_data)
    
    # 其他类型数据直接返回
    else:
        return json_data

def process_json_file(input_file, output_file):
    """
    处理JSON文件，去除值中的中括号标签内容并保存
    
    Args:
        input_file: 输入JSON文件路径
        output_file: 输出JSON文件路径
    """
    try:
        # 读取原始JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 去除中括号标签内容
        processed_data = remove_bracket_tags(data)
        
        # 保存处理后的JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, ensure_ascii=False, indent=2)
        
        print(f"处理完成！已将结果保存至 {output_file}")
        
    except FileNotFoundError:
        print(f"错误：未找到输入文件 {input_file}")
    except json.JSONDecodeError as e:
        print(f"JSON解析错误：{e}")
    except Exception as e:
        print(f"处理过程中发生错误：{e}")

def main():
    # 输入输出文件路径
    input_file = os.path.join('models', 'angel_investors_with_multi_encyclopedia.json')
    output_file = os.path.join('models', 'angel_investors_processed.json')
    
    print(f"开始处理文件：{input_file}")
    process_json_file(input_file, output_file)

if __name__ == "__main__":
    main()