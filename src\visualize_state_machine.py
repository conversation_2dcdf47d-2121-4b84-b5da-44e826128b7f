#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
状态机可视化脚本
绘制天使投资智能问答系统的有限状态自动转移图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def draw_state_machine():
    """绘制状态机转移图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    # 定义状态位置
    states = {
        'S0': {'pos': (1, 7), 'name': 'S0\n初始状态', 'color': '#E8F5E8'},
        'S1': {'pos': (3, 7), 'name': 'S1\n实体识别', 'color': '#E8F0FF'},
        'S2': {'pos': (5, 7), 'name': 'S2\n意图分析', 'color': '#E8F0FF'},
        'S3': {'pos': (7, 7), 'name': 'S3\n查询生成', 'color': '#E8F0FF'},
        'S4': {'pos': (9, 7), 'name': 'S4\n结果处理', 'color': '#E8F0FF'},
        'S5': {'pos': (7, 4), 'name': 'S5\n响应生成', 'color': '#E8F0FF'},
        'S6': {'pos': (5, 1), 'name': 'S6\n错误处理', 'color': '#FFE8E8'},
        'S7': {'pos': (9, 1), 'name': 'S7\n结束状态', 'color': '#FFE8E8'}
    }
    
    # 绘制状态节点
    state_boxes = {}
    for state_id, state_info in states.items():
        x, y = state_info['pos']
        
        # 创建圆角矩形
        box = FancyBboxPatch(
            (x-0.4, y-0.3), 0.8, 0.6,
            boxstyle="round,pad=0.1",
            facecolor=state_info['color'],
            edgecolor='black',
            linewidth=2
        )
        ax.add_patch(box)
        state_boxes[state_id] = box
        
        # 添加状态名称
        ax.text(x, y, state_info['name'], 
                ha='center', va='center', 
                fontsize=10, fontweight='bold')
    
    # 定义状态转移
    transitions = [
        # 正常流程
        ('S0', 'S1', '用户输入', 'normal'),
        ('S1', 'S2', '实体识别成功', 'normal'),
        ('S2', 'S3', '意图分析成功', 'normal'),
        ('S3', 'S4', '查询生成成功', 'normal'),
        ('S4', 'S5', '结果处理成功', 'normal'),
        ('S5', 'S7', '响应生成完成', 'normal'),
        
        # 错误处理流程
        ('S1', 'S6', '实体识别失败', 'error'),
        ('S2', 'S6', '意图分析失败', 'error'),
        ('S3', 'S6', '查询生成失败', 'error'),
        ('S4', 'S6', '结果处理失败', 'error'),
        ('S5', 'S6', '响应生成失败', 'error'),
        ('S6', 'S7', '错误处理完成', 'error'),
    ]
    
    # 绘制状态转移箭头
    for from_state, to_state, label, transition_type in transitions:
        from_pos = states[from_state]['pos']
        to_pos = states[to_state]['pos']
        
        # 设置箭头颜色
        if transition_type == 'normal':
            color = 'blue'
            linestyle = '-'
        else:
            color = 'red'
            linestyle = '--'
        
        # 计算箭头位置
        dx = to_pos[0] - from_pos[0]
        dy = to_pos[1] - from_pos[1]
        
        # 调整起点和终点位置（避免与状态框重叠）
        if abs(dx) > abs(dy):  # 水平方向
            if dx > 0:  # 向右
                start_x = from_pos[0] + 0.4
                end_x = to_pos[0] - 0.4
            else:  # 向左
                start_x = from_pos[0] - 0.4
                end_x = to_pos[0] + 0.4
            start_y = from_pos[1]
            end_y = to_pos[1]
        else:  # 垂直方向
            if dy > 0:  # 向上
                start_y = from_pos[1] + 0.3
                end_y = to_pos[1] - 0.3
            else:  # 向下
                start_y = from_pos[1] - 0.3
                end_y = to_pos[1] + 0.3
            start_x = from_pos[0]
            end_x = to_pos[0]
        
        # 绘制箭头
        arrow = patches.FancyArrowPatch(
            (start_x, start_y), (end_x, end_y),
            arrowstyle='->', 
            color=color,
            linestyle=linestyle,
            linewidth=2,
            mutation_scale=20
        )
        ax.add_patch(arrow)
        
        # 添加转移条件标签
        mid_x = (start_x + end_x) / 2
        mid_y = (start_y + end_y) / 2
        
        # 调整标签位置避免重叠
        if transition_type == 'error':
            offset_y = -0.2
        else:
            offset_y = 0.2
            
        ax.text(mid_x, mid_y + offset_y, label, 
                ha='center', va='center', 
                fontsize=8, 
                bbox=dict(boxstyle="round,pad=0.3", 
                         facecolor='white', 
                         edgecolor=color,
                         alpha=0.8))
    
    # 添加标题和说明
    ax.text(5, 7.7, '中国天使投资智能问答系统 - 有限状态自动转移图', 
            ha='center', va='center', 
            fontsize=16, fontweight='bold')
    
    # 添加图例
    legend_elements = [
        plt.Line2D([0], [0], color='blue', lw=2, label='正常流程'),
        plt.Line2D([0], [0], color='red', lw=2, linestyle='--', label='错误处理')
    ]
    ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))
    
    # 添加状态说明
    state_descriptions = [
        "S0: 系统启动，接收用户输入",
        "S1: 识别查询中的实体（投资机构、公司等）",
        "S2: 分析用户查询意图",
        "S3: 根据实体和意图生成Cypher查询",
        "S4: 执行查询并处理结果",
        "S5: 生成自然语言回答",
        "S6: 处理各种错误情况",
        "S7: 查询流程结束"
    ]
    
    for i, desc in enumerate(state_descriptions):
        ax.text(0.5, 3.5 - i*0.3, desc, 
                ha='left', va='center', 
                fontsize=9,
                bbox=dict(boxstyle="round,pad=0.2", 
                         facecolor='lightyellow', 
                         alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('state_machine_diagram.png', dpi=300, bbox_inches='tight')
    plt.show()

def print_state_machine_logic():
    """打印状态机运行逻辑说明"""
    
    print("🤖 中国天使投资智能问答系统 - 有限状态自动转移图")
    print("=" * 60)
    
    print("\n📋 状态定义:")
    states_info = {
        "S0 (初始状态)": "系统启动状态，接收用户输入的自然语言查询",
        "S1 (实体识别)": "使用正则表达式识别查询中的实体（投资机构、公司、轮次等）",
        "S2 (意图分析)": "基于关键词匹配分析用户查询意图",
        "S3 (查询生成)": "根据识别的实体和意图生成对应的Cypher查询语句",
        "S4 (结果处理)": "执行Cypher查询并获取Neo4j数据库结果",
        "S5 (响应生成)": "将查询结果转换为自然语言回答",
        "S6 (错误处理)": "处理各个环节可能出现的错误情况",
        "S7 (结束状态)": "查询流程完成，返回最终结果"
    }
    
    for state, description in states_info.items():
        print(f"  {state}: {description}")
    
    print("\n🔄 状态转移规则:")
    transitions_info = [
        ("S0 → S1", "用户输入查询后，自动进入实体识别状态"),
        ("S1 → S2", "实体识别成功后，进入意图分析状态"),
        ("S1 → S6", "实体识别失败时，进入错误处理状态"),
        ("S2 → S3", "意图分析成功后，进入查询生成状态"),
        ("S2 → S6", "意图分析失败时，进入错误处理状态"),
        ("S3 → S4", "查询生成成功后，进入结果处理状态"),
        ("S3 → S6", "查询生成失败时，进入错误处理状态"),
        ("S4 → S5", "结果处理成功后，进入响应生成状态"),
        ("S4 → S6", "结果处理失败时，进入错误处理状态"),
        ("S5 → S7", "响应生成完成后，进入结束状态"),
        ("S5 → S6", "响应生成失败时，进入错误处理状态"),
        ("S6 → S7", "错误处理完成后，进入结束状态")
    ]
    
    for transition, description in transitions_info:
        print(f"  {transition}: {description}")
    
    print("\n💡 核心算法逻辑:")
    algorithms = [
        "1. 实体识别: 使用预定义的正则表达式模式匹配投资机构、公司名称等实体",
        "2. 意图分析: 基于关键词匹配计算不同查询意图的得分，选择最高分意图",
        "3. 查询生成: 根据实体类型和查询意图，动态构建Cypher查询语句",
        "4. 结果处理: 执行Neo4j查询并验证结果有效性",
        "5. 响应生成: 根据查询意图格式化结果为自然语言回答",
        "6. 错误处理: 提供友好的错误提示和建议"
    ]
    
    for algorithm in algorithms:
        print(f"  {algorithm}")
    
    print("\n🎯 支持的查询类型:")
    query_types = [
        "• 投资机构信息查询: '华控基金的信息'",
        "• 公司信息查询: '水木源华公司的详细信息'", 
        "• 投资关系查询: '华控基金投资了哪些公司？'",
        "• 统计分析查询: '最活跃的投资机构有哪些？'",
        "• 网络分析查询: '投资网络中的关键节点'"
    ]
    
    for query_type in query_types:
        print(f"  {query_type}")

if __name__ == "__main__":
    print_state_machine_logic()
    print("\n🎨 正在生成状态转移图...")
    draw_state_machine()
    print("✅ 状态转移图已保存为 'state_machine_diagram.png'")
