#!/usr/bin/env python
# -*- coding: utf-8 -*-

import socket
import sys
import subprocess
import platform
import os
from urllib.parse import urlparse

def check_neo4j_service(uri="bolt://localhost:7687"):
    """
    检查Neo4j服务是否正在运行
    
    Args:
        uri: Neo4j数据库URI
        
    Returns:
        是否正在运行
    """
    print(f"检查Neo4j服务: {uri}")
    
    # 解析URI
    parsed_uri = urlparse(uri)
    host = parsed_uri.hostname or 'localhost'
    port = parsed_uri.port or 7687
    
    # 检查端口是否开放
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✓ Neo4j服务正在运行，端口 {port} 已开放")
            return True
        else:
            print(f"✗ Neo4j服务未运行或端口 {port} 未开放")
            return False
    except Exception as e:
        print(f"✗ 检查Neo4j服务时出错: {e}")
        return False

def get_neo4j_status():
    """
    获取Neo4j服务状态
    
    Returns:
        状态信息
    """
    system = platform.system()
    
    if system == "Windows":
        try:
            # 检查Neo4j服务是否已安装
            result = subprocess.run(
                ["sc", "query", "neo4j"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            if "RUNNING" in result.stdout:
                return "Neo4j服务已安装并正在运行"
            elif "STOPPED" in result.stdout:
                return "Neo4j服务已安装但未运行"
            else:
                return "Neo4j服务未安装或无法访问"
        except Exception:
            return "无法检查Neo4j服务状态"
    elif system == "Linux" or system == "Darwin":  # Linux or macOS
        try:
            # 检查Neo4j进程是否在运行
            result = subprocess.run(
                ["ps", "-ef"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            if "neo4j" in result.stdout:
                return "Neo4j进程正在运行"
            else:
                return "Neo4j进程未运行"
        except Exception:
            return "无法检查Neo4j进程状态"
    else:
        return "未知操作系统，无法检查Neo4j服务状态"

def print_help_info():
    """
    打印帮助信息
    """
    print("\n如何启动Neo4j服务:")
    
    system = platform.system()
    
    if system == "Windows":
        print("方法1: 使用Windows服务")
        print("  1. 打开服务管理器: 按下Win+R，输入services.msc，然后按Enter")
        print("  2. 找到Neo4j服务，右键点击，选择'启动'")
        print("\n方法2: 使用命令行")
        print("  1. 以管理员身份打开命令提示符")
        print("  2. 运行命令: net start neo4j")
        print("\n方法3: 使用Neo4j桌面应用")
        print("  1. 打开Neo4j Desktop")
        print("  2. 选择您的数据库，点击'Start'按钮")
    elif system == "Linux":
        print("方法1: 使用systemd服务")
        print("  sudo systemctl start neo4j")
        print("\n方法2: 使用Neo4j命令")
        print("  sudo neo4j start")
    elif system == "Darwin":  # macOS
        print("方法1: 使用Homebrew")
        print("  brew services start neo4j")
        print("\n方法2: 使用Neo4j命令")
        print("  neo4j start")
    else:
        print("请参考Neo4j官方文档，了解如何在您的操作系统上启动Neo4j服务")
    
    print("\n如何检查Neo4j配置:")
    print("1. 找到neo4j.conf文件")
    if system == "Windows":
        print("   通常位于: C:\\Program Files\\Neo4j\\conf\\neo4j.conf")
    elif system == "Linux":
        print("   通常位于: /etc/neo4j/neo4j.conf")
    elif system == "Darwin":  # macOS
        print("   通常位于: /usr/local/Cellar/neo4j/*/libexec/conf/neo4j.conf")
    
    print("2. 检查以下配置项:")
    print("   - dbms.connector.bolt.enabled=true")
    print("   - dbms.connector.bolt.listen_address=0.0.0.0:7687")
    print("   - dbms.connector.bolt.advertised_address=:7687")
    print("   - dbms.default_listen_address=0.0.0.0")
    
    print("\n如何重置Neo4j密码:")
    if system == "Windows":
        print("1. 停止Neo4j服务")
        print("2. 打开命令提示符，导航到Neo4j安装目录的bin文件夹")
        print("3. 运行命令: neo4j-admin set-initial-password 新密码")
        print("4. 启动Neo4j服务")
    else:
        print("1. 停止Neo4j服务")
        print("2. 运行命令: neo4j-admin set-initial-password 新密码")
        print("3. 启动Neo4j服务")

def main():
    """主函数"""
    print("Neo4j服务检查工具")
    print("================")
    
    # 检查Neo4j服务是否正在运行
    uri = "bolt://localhost:7687"
    if len(sys.argv) > 1:
        uri = sys.argv[1]
    
    is_running = check_neo4j_service(uri)
    
    # 获取Neo4j服务状态
    status = get_neo4j_status()
    print(f"服务状态: {status}")
    
    # 如果服务未运行，打印帮助信息
    if not is_running:
        print_help_info()
    
    return 0 if is_running else 1

if __name__ == "__main__":
    sys.exit(main())
