#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j服务检查脚本
检查Neo4j服务是否正常运行
"""

import requests
import time
import logging
from py2neo import Graph

logger = logging.getLogger(__name__)

def check_neo4j_http(host="localhost", port=7474):
    """检查Neo4j HTTP服务"""
    try:
        url = f"http://{host}:{port}"
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            logger.info(f"Neo4j HTTP服务正常运行: {url}")
            return True
        else:
            logger.warning(f"Neo4j HTTP服务响应异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"无法连接到Neo4j HTTP服务: {e}")
        return False

def check_neo4j_bolt(uri="bolt://localhost:7687", user="neo4j", password="angelinvestment"):
    """检查Neo4j Bolt连接"""
    try:
        graph = Graph(uri, auth=(user, password))
        result = graph.run("RETURN 1").data()
        if result and result[0]['1'] == 1:
            logger.info(f"Neo4j Bolt连接正常: {uri}")
            return True
        else:
            logger.warning("Neo4j Bolt连接异常")
            return False
    except Exception as e:
        logger.error(f"无法连接到Neo4j Bolt服务: {e}")
        return False

def get_neo4j_version(uri="bolt://localhost:7687", user="neo4j", password="angelinvestment"):
    """获取Neo4j版本信息"""
    try:
        graph = Graph(uri, auth=(user, password))
        result = graph.run("CALL dbms.components()").data()
        for component in result:
            if component['name'] == 'Neo4j Kernel':
                version = component['versions'][0]
                logger.info(f"Neo4j版本: {version}")
                return version
        return None
    except Exception as e:
        logger.error(f"获取Neo4j版本失败: {e}")
        return None

def get_database_info(uri="bolt://localhost:7687", user="neo4j", password="angelinvestment"):
    """获取数据库信息"""
    try:
        graph = Graph(uri, auth=(user, password))
        
        # 获取节点数量
        node_count = graph.run("MATCH (n) RETURN count(n) as count").data()[0]['count']
        
        # 获取关系数量
        rel_count = graph.run("MATCH ()-[r]->() RETURN count(r) as count").data()[0]['count']
        
        # 获取标签信息
        labels = graph.run("CALL db.labels()").data()
        label_list = [item['label'] for item in labels]
        
        # 获取关系类型
        rel_types = graph.run("CALL db.relationshipTypes()").data()
        rel_type_list = [item['relationshipType'] for item in rel_types]
        
        logger.info(f"数据库统计:")
        logger.info(f"  节点数量: {node_count}")
        logger.info(f"  关系数量: {rel_count}")
        logger.info(f"  节点标签: {label_list}")
        logger.info(f"  关系类型: {rel_type_list}")
        
        return {
            'node_count': node_count,
            'relationship_count': rel_count,
            'labels': label_list,
            'relationship_types': rel_type_list
        }
        
    except Exception as e:
        logger.error(f"获取数据库信息失败: {e}")
        return None

def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    logger.info("开始检查Neo4j服务...")
    
    # 检查HTTP服务
    http_ok = check_neo4j_http()
    
    # 检查Bolt连接
    bolt_ok = check_neo4j_bolt()
    
    if http_ok and bolt_ok:
        logger.info("Neo4j服务运行正常")
        
        # 获取版本信息
        version = get_neo4j_version()
        
        # 获取数据库信息
        db_info = get_database_info()
        
        return True
    else:
        logger.error("Neo4j服务异常，请检查服务是否启动")
        logger.info("启动Neo4j的方法:")
        logger.info("1. Windows: 在Neo4j安装目录运行 bin/neo4j.bat console")
        logger.info("2. Linux/Mac: 在Neo4j安装目录运行 bin/neo4j console")
        logger.info("3. Docker: docker run -p 7474:7474 -p 7687:7687 neo4j")
        return False

if __name__ == "__main__":
    main()
