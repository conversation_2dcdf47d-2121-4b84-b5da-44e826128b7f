#!/bin/bash

echo "中国天使投资知识图谱本体可视化"
echo "=============================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python环境，请安装Python 3.6+"
    exit 1
fi

# 检查Graphviz
if ! command -v dot &> /dev/null; then
    echo "错误: 未找到Graphviz，请安装Graphviz"
    echo "可以从 https://graphviz.org/download/ 下载安装Graphviz"
    echo "或者使用包管理器安装，例如:"
    echo "  Ubuntu/Debian: sudo apt-get install graphviz"
    echo "  CentOS/RHEL: sudo yum install graphviz"
    echo "  macOS: brew install graphviz"
    exit 1
fi

echo
echo "请选择输出格式:"
echo "1. PNG格式 (位图，适合查看)"
echo "2. SVG格式 (矢量图，适合编辑)"
echo "3. PDF格式 (适合打印)"
echo "4. 退出"

read -p "请输入选项 (1-4): " format

case $format in
    1)
        echo "正在生成PNG格式的本体图..."
        python3 src/visualize_ontology_updated.py --format png
        ;;
    2)
        echo "正在生成SVG格式的本体图..."
        python3 src/visualize_ontology_updated.py --format svg
        ;;
    3)
        echo "正在生成PDF格式的本体图..."
        python3 src/visualize_ontology_updated.py --format pdf
        ;;
    4)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo "无效的选项!"
        exit 1
        ;;
esac

echo
echo "本体图生成完成!"
echo "结果保存在 models/angel_investment_ontology_updated.xxx"

read -p "按任意键继续..." key
