@echo off
echo 中国天使投资知识图谱本体可视化
echo ==============================

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请安装Python 3.6+
    pause
    exit /b 1
)

REM 检查Graphviz
dot -V >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Graphviz，请安装Graphviz并将其添加到PATH环境变量中
    echo 可以从 https://graphviz.org/download/ 下载安装Graphviz
    pause
    exit /b 1
)

echo.
echo 请选择输出格式:
echo 1. PNG格式 (位图，适合查看)
echo 2. SVG格式 (矢量图，适合编辑)
echo 3. PDF格式 (适合打印)
echo 4. 退出

set /p format="请输入选项 (1-4): "

if "%format%"=="1" (
    echo 正在生成PNG格式的本体图...
    python src/visualize_ontology_updated.py --format png
) else if "%format%"=="2" (
    echo 正在生成SVG格式的本体图...
    python src/visualize_ontology_updated.py --format svg
) else if "%format%"=="3" (
    echo 正在生成PDF格式的本体图...
    python src/visualize_ontology_updated.py --format pdf
) else if "%format%"=="4" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效的选项!
    pause
    exit /b 1
)

echo.
echo 本体图生成完成!
echo 结果保存在 models/angel_investment_ontology_updated.xxx

pause
