# 中国天使投资知识图谱 - Neo4j直接导入指南

本文档详细说明了如何使用Python脚本直接将CSV数据导入到Neo4j图数据库中，无需使用`neo4j-admin`命令。

## 1. 概述

直接导入过程使用Neo4j的Python驱动程序（py2neo）将CSV数据导入到Neo4j数据库中。这种方法的优点是：

- 不需要停止Neo4j服务
- 不需要管理员权限
- 可以导入到已有数据的数据库中
- 可以在导入过程中执行数据转换和验证

## 2. 准备工作

### 2.1 环境要求

- Python 3.6+
- Neo4j 3.5+（已启动并运行）
- py2neo库

### 2.2 安装依赖

在使用导入工具之前，需要安装py2neo库：

```bash
pip install py2neo
```

批处理脚本会自动检查并安装py2neo库，如果尚未安装。

### 2.3 CSV文件准备

导入工具假设CSV文件已经准备好，并存放在`neo4j_import`目录中。需要以下CSV文件：

- `angel_investor.csv`：天使投资机构节点
- `venture_capital.csv`：风险投资机构节点
- `company.csv`：创业公司节点
- `investment_round.csv`：投资轮次节点
- `investment_event.csv`：投资事件节点
- `invests_relationship.csv`：投资关系
- `has_event_relationship.csv`：投资事件关系
- `has_round_relationship.csv`：投资轮次关系

CSV文件的格式应该符合Neo4j的要求，包含以下字段：

- 节点CSV文件：`id:ID`, `name`, `description`, `:LABEL`等
- 关系CSV文件：`:START_ID`, `:END_ID`, `name`, `:TYPE`等

## 3. 使用方法

### 3.1 使用批处理脚本（推荐）

我们提供了批处理脚本，方便用户输入Neo4j连接信息并选择导入选项：

#### Windows

双击运行`run_direct_import.bat`，然后按照提示输入Neo4j连接信息并选择导入选项：

```
中国天使投资知识图谱 - Neo4j直接导入工具
======================================

请输入Neo4j数据库连接信息:
Neo4j URI [bolt://localhost:7687]: 
用户名 [neo4j]: 
密码 [password]: 

请选择导入选项:
1. 导入前清空数据库
2. 保留现有数据
```

#### Linux/macOS

在终端中运行：

```bash
chmod +x run_direct_import.sh  # 添加执行权限
./run_direct_import.sh
```

然后按照提示输入Neo4j连接信息并选择导入选项。

### 3.2 直接使用Python脚本

您也可以直接使用Python脚本，这样可以更灵活地控制导入过程：

```bash
# 基本用法
python src/direct_import_to_neo4j.py

# 指定Neo4j连接信息
python src/direct_import_to_neo4j.py --uri="bolt://localhost:7687" --user="neo4j" --password="password"

# 导入前清空数据库
python src/direct_import_to_neo4j.py --clear

# 指定CSV文件目录
python src/direct_import_to_neo4j.py --csv-dir="my_csv_files"

# 不创建索引
python src/direct_import_to_neo4j.py --no-indexes
```

### 3.3 命令行参数

Python脚本支持以下命令行参数：

- `--uri`：指定Neo4j数据库URI，默认为`bolt://localhost:7687`。
- `--user`：指定Neo4j用户名，默认为`neo4j`。
- `--password`：指定Neo4j密码，默认为`password`。
- `--csv-dir`：指定CSV文件目录，默认为`neo4j_import`。
- `--clear`：导入前清空数据库。
- `--no-indexes`：不创建索引。

## 4. 导入过程详解

### 4.1 连接到Neo4j数据库

导入工具首先会连接到Neo4j数据库，使用提供的URI、用户名和密码。如果连接失败，导入过程将终止。

### 4.2 清空数据库（可选）

如果指定了`--clear`参数，导入工具会在导入前清空数据库中的所有数据。这是通过执行以下Cypher查询实现的：

```cypher
MATCH (n) DETACH DELETE n
```

### 4.3 导入节点数据

导入工具会读取节点CSV文件，并将节点数据导入到Neo4j数据库中。导入过程会为每个节点设置标签和属性。

### 4.4 导入关系数据

导入工具会读取关系CSV文件，并将关系数据导入到Neo4j数据库中。导入过程会查找起始节点和结束节点，然后创建它们之间的关系。

### 4.5 创建索引（可选）

如果没有指定`--no-indexes`参数，导入工具会为常用的节点属性创建索引，以提高查询性能。创建的索引包括：

- 为每种节点类型的`id`属性创建索引
- 为`name`属性创建索引

## 5. 故障排除

### 5.1 常见问题

#### 连接失败

如果无法连接到Neo4j数据库，请检查：

- Neo4j服务是否已启动
- URI、用户名和密码是否正确
- 防火墙是否允许连接

#### 导入失败

如果导入过程失败，请检查日志文件`neo4j_import.log`，查看详细的错误信息。常见的导入失败原因包括：

- CSV文件格式错误
- 找不到起始节点或结束节点
- 内存不足

#### py2neo安装失败

如果py2neo安装失败，请尝试手动安装：

```bash
pip install py2neo
```

或者使用管理员权限安装：

```bash
# Windows
pip install py2neo --user

# Linux/macOS
sudo pip install py2neo
```

### 5.2 日志文件

导入工具会生成一个日志文件`neo4j_import.log`，记录导入过程中的详细信息。如果遇到问题，请查看这个日志文件。

## 6. 导入后的操作

### 6.1 验证导入结果

导入完成后，您可以使用Neo4j Browser验证导入结果：

1. 打开浏览器，访问`http://localhost:7474`
2. 使用Neo4j用户名和密码登录
3. 运行以下Cypher查询，检查导入的数据：

```cypher
// 查看节点数量
MATCH (n) RETURN labels(n) AS NodeType, count(*) AS Count;

// 查看关系数量
MATCH ()-[r]->() RETURN type(r) AS RelationType, count(*) AS Count;

// 查看天使投资机构
MATCH (a:AngelInvestor) RETURN a.name LIMIT 10;

// 查看投资关系
MATCH (i)-[r:INVESTS]->(c) RETURN i.name, c.name LIMIT 10;
```

### 6.2 数据分析

导入完成后，您可以使用Cypher查询语言对数据进行分析。例如：

```cypher
// 查询投资最多的机构
MATCH (i)-[r:INVESTS]->()
RETURN i.name, count(r) AS investment_count
ORDER BY investment_count DESC
LIMIT 10;

// 查询获得投资最多的公司
MATCH ()-[r:INVESTS]->(c)
RETURN c.name, count(r) AS investor_count
ORDER BY investor_count DESC
LIMIT 10;

// 查询各轮次的投资事件数量
MATCH (e:InvestmentEvent)-[:HAS_ROUND]->(r)
RETURN r.name, count(e) AS event_count
ORDER BY event_count DESC;
```

## 7. 结论

通过本指南，您应该能够成功地将CSV数据直接导入到Neo4j图数据库中，无需使用`neo4j-admin`命令。这种方法更加灵活，可以在不停止Neo4j服务的情况下导入数据，并且可以在导入过程中执行数据转换和验证。

如果您在导入过程中遇到任何问题，请查看日志文件或联系技术支持。
