#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j样式应用脚本
自动将样式配置应用到Neo4j数据库
"""

import os
import argparse
from py2neo import Graph

def apply_style_to_neo4j(uri, user, password, database=None, style_file="neo4j_style.grass"):
    """
    将样式配置应用到Neo4j数据库
    
    Args:
        uri: Neo4j数据库URI
        user: 用户名
        password: 密码
        database: 数据库名称
        style_file: 样式文件路径
    """
    try:
        # 连接到Neo4j数据库
        if database:
            graph = Graph(uri, auth=(user, password), name=database)
        else:
            graph = Graph(uri, auth=(user, password))
        
        print(f"已连接到Neo4j数据库: {uri}")
        
        # 读取样式文件
        if not os.path.exists(style_file):
            print(f"样式文件不存在: {style_file}")
            return False
        
        with open(style_file, 'r', encoding='utf-8') as f:
            style_content = f.read()
        
        print(f"已读取样式文件: {style_file}")
        
        # 创建一些示例查询来验证数据
        print("\n验证数据库中的数据...")
        
        # 检查节点数量
        result = graph.run("MATCH (n) RETURN labels(n) as labels, count(n) as count")
        node_counts = {}
        for record in result:
            labels = record['labels']
            count = record['count']
            if labels:
                label = labels[0]  # 取第一个标签
                node_counts[label] = count
        
        if node_counts:
            print("数据库中的节点统计:")
            for label, count in node_counts.items():
                print(f"  {label}: {count} 个节点")
        else:
            print("数据库中没有找到节点，请先导入数据")
            return False
        
        # 检查关系数量
        result = graph.run("MATCH ()-[r]->() RETURN type(r) as type, count(r) as count")
        rel_counts = {}
        for record in result:
            rel_type = record['type']
            count = record['count']
            rel_counts[rel_type] = count
        
        if rel_counts:
            print("\n数据库中的关系统计:")
            for rel_type, count in rel_counts.items():
                print(f"  {rel_type}: {count} 个关系")
        
        print(f"\n样式配置内容:")
        print("=" * 50)
        print(style_content)
        print("=" * 50)
        
        print("\n请按照以下步骤在Neo4j Browser中应用样式:")
        print("1. 打开Neo4j Browser (http://localhost:7474)")
        print("2. 点击右上角的设置图标（齿轮图标）")
        print("3. 选择 'Graph Visualization'")
        print("4. 将上面显示的样式内容复制粘贴到样式编辑器中")
        print("5. 点击 'Apply' 应用样式")
        print("\n或者在Neo4j Browser中执行以下命令:")
        print(":style reset")
        print(f":style {style_file}")
        
        return True
        
    except Exception as e:
        print(f"应用样式时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='应用Neo4j样式配置')
    parser.add_argument('--uri', default='bolt://localhost:7687', help='Neo4j数据库URI')
    parser.add_argument('--user', default='neo4j', help='用户名')
    parser.add_argument('--password', default='angelinvestment', help='密码')
    parser.add_argument('--database', default='angelinvestment', help='数据库名称')
    parser.add_argument('--style-file', default='neo4j_style.grass', help='样式文件路径')
    
    args = parser.parse_args()
    
    print("Neo4j样式应用工具")
    print("=" * 50)
    
    success = apply_style_to_neo4j(
        args.uri, 
        args.user, 
        args.password, 
        args.database, 
        args.style_file
    )
    
    if success:
        print("\n样式配置准备完成！")
        print("请在Neo4j Browser中手动应用样式配置。")
    else:
        print("\n样式配置失败，请检查数据库连接和数据。")

if __name__ == "__main__":
    main()
