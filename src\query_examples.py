#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j查询示例脚本
展示如何查询和显示节点的详细属性
"""

import argparse
from py2neo import Graph
import json

def connect_to_neo4j(uri, user, password, database=None):
    """连接到Neo4j数据库"""
    try:
        if database:
            graph = Graph(uri, auth=(user, password), name=database)
        else:
            graph = Graph(uri, auth=(user, password))
        
        print(f"已连接到Neo4j数据库: {uri}")
        return graph
    except Exception as e:
        print(f"连接数据库失败: {e}")
        return None

def query_investor_details(graph, investor_name=None):
    """查询投资机构详细信息"""
    print("\n=== 投资机构详细信息 ===")
    
    if investor_name:
        query = """
        MATCH (investor)
        WHERE investor.name CONTAINS $name AND (investor:AngelInvestor OR investor:VentureCapital)
        RETURN investor
        LIMIT 5
        """
        result = graph.run(query, name=investor_name)
    else:
        query = """
        MATCH (investor)
        WHERE investor:AngelInvestor OR investor:VentureCapital
        RETURN investor
        LIMIT 5
        """
        result = graph.run(query)
    
    for record in result:
        investor = record['investor']
        print(f"\n机构名称: {investor.get('name', 'N/A')}")
        print(f"描述: {investor.get('description', 'N/A')}")
        print("-" * 50)

def query_company_details(graph, company_name=None):
    """查询公司详细信息"""
    print("\n=== 创业公司详细信息 ===")
    
    if company_name:
        query = """
        MATCH (company:StartupCompany)
        WHERE company.name CONTAINS $name
        RETURN company
        LIMIT 5
        """
        result = graph.run(query, name=company_name)
    else:
        query = """
        MATCH (company:StartupCompany)
        RETURN company
        LIMIT 5
        """
        result = graph.run(query)
    
    for record in result:
        company = record['company']
        print(f"\n公司名称: {company.get('name', 'N/A')}")
        print(f"描述: {company.get('description', 'N/A')}")
        print("-" * 50)

def query_investment_relationships(graph):
    """查询投资关系详细信息"""
    print("\n=== 投资关系详细信息 ===")
    
    query = """
    MATCH (investor)-[r:INVESTS]->(company:StartupCompany)
    RETURN investor.name as 投资机构, 
           company.name as 被投公司,
           r.investmentDate as 投资日期,
           r.roundType as 投资轮次,
           r.investmentAmount as 投资金额
    LIMIT 10
    """
    
    result = graph.run(query)
    
    for record in result:
        print(f"投资机构: {record['投资机构']}")
        print(f"被投公司: {record['被投公司']}")
        print(f"投资日期: {record['投资日期'] or 'N/A'}")
        print(f"投资轮次: {record['投资轮次'] or 'N/A'}")
        print(f"投资金额: {record['投资金额'] or 'N/A'}")
        print("-" * 30)

def query_related_nodes(graph, node_name):
    """查询节点的相关节点"""
    print(f"\n=== {node_name} 的相关节点 ===")
    
    query = """
    MATCH (n)-[r]-(related)
    WHERE n.name CONTAINS $name
    RETURN n, type(r) as 关系类型, related, labels(related) as 相关节点类型
    LIMIT 20
    """
    
    result = graph.run(query, name=node_name)
    
    for record in result:
        node = record['n']
        rel_type = record['关系类型']
        related = record['related']
        related_labels = record['相关节点类型']
        
        print(f"节点: {node.get('name', 'N/A')}")
        print(f"关系: {rel_type}")
        print(f"相关节点: {related.get('name', 'N/A')} ({related_labels[0] if related_labels else 'Unknown'})")
        print("-" * 30)

def query_statistics(graph):
    """查询统计信息"""
    print("\n=== 数据统计信息 ===")
    
    # 节点统计
    query = "MATCH (n) RETURN labels(n) as labels, count(n) as count"
    result = graph.run(query)
    
    print("节点统计:")
    for record in result:
        labels = record['labels']
        count = record['count']
        if labels:
            print(f"  {labels[0]}: {count} 个")
    
    # 关系统计
    query = "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
    result = graph.run(query)
    
    print("\n关系统计:")
    for record in result:
        rel_type = record['type']
        count = record['count']
        print(f"  {rel_type}: {count} 个")
    
    # 行业分布
    query = """
    MATCH (industry:Industry)<-[:BELONGS_TO_INDUSTRY]-(investor)
    RETURN industry.name as industry, count(investor) as count
    ORDER BY count DESC
    LIMIT 10
    """
    result = graph.run(query)
    
    print("\n行业分布 (前10名):")
    for record in result:
        industry = record['industry']
        count = record['count']
        print(f"  {industry}: {count} 家机构")

def query_network_example(graph):
    """展示网络查询示例"""
    print("\n=== 网络查询示例 ===")
    
    # 查询投资机构及其相关信息
    query = """
    MATCH (investor:Investor)-[:FOUNDED_BY]->(founder:Person)
    RETURN investor.name as 机构名称, founder.name as 创始人
    LIMIT 10
    """
    
    result = graph.run(query)
    
    print("投资机构创始人信息:")
    for record in result:
        print(f"  {record['机构名称']} - 创始人: {record['创始人']}")
    
    # 查询地区分布
    query = """
    MATCH (investor:Investor)-[:LOCATED_IN]->(location:Location)
    RETURN location.name as 地区, count(investor) as 机构数量
    ORDER BY 机构数量 DESC
    LIMIT 10
    """
    
    result = graph.run(query)
    
    print("\n地区分布:")
    for record in result:
        print(f"  {record['地区']}: {record['机构数量']} 家")

def main():
    parser = argparse.ArgumentParser(description='Neo4j查询示例')
    parser.add_argument('--uri', default='bolt://localhost:7687', help='Neo4j数据库URI')
    parser.add_argument('--user', default='neo4j', help='用户名')
    parser.add_argument('--password', default='angelinvestment', help='密码')
    parser.add_argument('--database', default='', help='数据库名称')
    parser.add_argument('--investor', help='查询特定投资机构')
    parser.add_argument('--company', help='查询特定公司')
    parser.add_argument('--related', help='查询特定节点的相关节点')
    
    args = parser.parse_args()
    
    print("Neo4j查询示例工具")
    print("=" * 50)
    
    # 连接数据库
    graph = connect_to_neo4j(args.uri, args.user, args.password, args.database if args.database else None)
    if not graph:
        return
    
    # 执行查询
    query_statistics(graph)
    query_investor_details(graph, args.investor)
    query_company_details(graph, args.company)
    query_investment_relationships(graph)
    query_network_example(graph)
    
    if args.related:
        query_related_nodes(graph, args.related)
    
    print("\n查询完成！")
    print("\n在Neo4j Browser中，您可以:")
    print("1. 点击任意节点查看其详细属性")
    print("2. 双击节点展开相关节点")
    print("3. 使用以下查询语句:")
    print("   MATCH (n:Investor) RETURN n LIMIT 25")
    print("   MATCH (n:StartupCompany) RETURN n LIMIT 25")
    print("   MATCH (investor)-[r]-(related) WHERE investor.name CONTAINS '华控' RETURN investor, r, related LIMIT 50")

if __name__ == "__main__":
    main()
