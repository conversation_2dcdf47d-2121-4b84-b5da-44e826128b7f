#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j查询示例脚本
展示如何查询和显示节点的详细属性
"""

import argparse
from py2neo import Graph
import json

def connect_to_neo4j(uri, user, password, database=None):
    """连接到Neo4j数据库"""
    try:
        if database:
            graph = Graph(uri, auth=(user, password), name=database)
        else:
            graph = Graph(uri, auth=(user, password))
        
        print(f"已连接到Neo4j数据库: {uri}")
        return graph
    except Exception as e:
        print(f"连接数据库失败: {e}")
        return None

def query_investor_details(graph, investor_name=None):
    """查询投资机构详细信息"""
    print("\n=== 投资机构详细信息 ===")
    
    if investor_name:
        query = """
        MATCH (investor)
        WHERE investor.name CONTAINS $name AND (investor:AngelInvestor OR investor:VentureCapital)
        RETURN investor
        LIMIT 5
        """
        result = graph.run(query, name=investor_name)
    else:
        query = """
        MATCH (investor)
        WHERE investor:AngelInvestor OR investor:VentureCapital
        RETURN investor
        LIMIT 5
        """
        result = graph.run(query)
    
    for record in result:
        investor = record['investor']
        print(f"\n机构名称: {investor.get('name', 'N/A')}")
        print(f"成立日期: {investor.get('foundingDate', 'N/A')}")
        print(f"公司类型: {investor.get('companyType', 'N/A')}")
        print(f"所属行业: {investor.get('industry', 'N/A')}")
        print(f"经营范围: {investor.get('businessScope', 'N/A')}")
        print(f"总部地点: {investor.get('location', 'N/A')}")
        print(f"创始人: {investor.get('founder', 'N/A')}")
        print(f"法定代表人: {investor.get('legalRepresentative', 'N/A')}")
        print(f"注册资本: {investor.get('registeredCapital', 'N/A')}")
        print(f"描述: {investor.get('description', 'N/A')}")
        print("-" * 50)

def query_company_details(graph, company_name=None):
    """查询公司详细信息"""
    print("\n=== 创业公司详细信息 ===")
    
    if company_name:
        query = """
        MATCH (company:StartupCompany)
        WHERE company.name CONTAINS $name
        RETURN company
        LIMIT 5
        """
        result = graph.run(query, name=company_name)
    else:
        query = """
        MATCH (company:StartupCompany)
        RETURN company
        LIMIT 5
        """
        result = graph.run(query)
    
    for record in result:
        company = record['company']
        print(f"\n公司名称: {company.get('name', 'N/A')}")
        print(f"成立日期: {company.get('foundingDate', 'N/A')}")
        print(f"公司类型: {company.get('companyType', 'N/A')}")
        print(f"所属行业: {company.get('industry', 'N/A')}")
        print(f"经营范围: {company.get('businessScope', 'N/A')}")
        print(f"总部地点: {company.get('location', 'N/A')}")
        print(f"创始人: {company.get('founder', 'N/A')}")
        print(f"法定代表人: {company.get('legalRepresentative', 'N/A')}")
        print(f"注册资本: {company.get('registeredCapital', 'N/A')}")
        print(f"描述: {company.get('description', 'N/A')}")
        print("-" * 50)

def query_investment_relationships(graph):
    """查询投资关系详细信息"""
    print("\n=== 投资关系详细信息 ===")
    
    query = """
    MATCH (investor)-[r:INVESTS]->(company:StartupCompany)
    RETURN investor.name as 投资机构, 
           company.name as 被投公司,
           r.investmentDate as 投资日期,
           r.roundType as 投资轮次,
           r.investmentAmount as 投资金额
    LIMIT 10
    """
    
    result = graph.run(query)
    
    for record in result:
        print(f"投资机构: {record['投资机构']}")
        print(f"被投公司: {record['被投公司']}")
        print(f"投资日期: {record['投资日期'] or 'N/A'}")
        print(f"投资轮次: {record['投资轮次'] or 'N/A'}")
        print(f"投资金额: {record['投资金额'] or 'N/A'}")
        print("-" * 30)

def query_investment_rounds(graph):
    """查询投资轮次信息"""
    print("\n=== 投资轮次信息 ===")
    
    query = """
    MATCH (round)
    WHERE round:AngelRound OR round:SeedRound OR round:RoundA OR round:RoundB OR round:RoundC OR round:InvestmentRound
    RETURN round
    ORDER BY round.name
    """
    
    result = graph.run(query)
    
    for record in result:
        round_node = record['round']
        print(f"轮次名称: {round_node.get('name', 'N/A')}")
        print(f"轮次类型: {round_node.get('roundType', 'N/A')}")
        print(f"投资阶段: {round_node.get('stage', 'N/A')}")
        print(f"描述: {round_node.get('description', 'N/A')}")
        print("-" * 30)

def query_statistics(graph):
    """查询统计信息"""
    print("\n=== 数据统计信息 ===")
    
    # 节点统计
    query = "MATCH (n) RETURN labels(n) as labels, count(n) as count"
    result = graph.run(query)
    
    print("节点统计:")
    for record in result:
        labels = record['labels']
        count = record['count']
        if labels:
            print(f"  {labels[0]}: {count} 个")
    
    # 关系统计
    query = "MATCH ()-[r]->() RETURN type(r) as type, count(r) as count"
    result = graph.run(query)
    
    print("\n关系统计:")
    for record in result:
        rel_type = record['type']
        count = record['count']
        print(f"  {rel_type}: {count} 个")
    
    # 行业分布
    query = """
    MATCH (company:StartupCompany)
    WHERE company.industry IS NOT NULL AND company.industry <> ""
    RETURN company.industry as industry, count(*) as count
    ORDER BY count DESC
    LIMIT 10
    """
    result = graph.run(query)
    
    print("\n行业分布 (前10名):")
    for record in result:
        industry = record['industry']
        count = record['count']
        print(f"  {industry}: {count} 家公司")

def main():
    parser = argparse.ArgumentParser(description='Neo4j查询示例')
    parser.add_argument('--uri', default='bolt://localhost:7687', help='Neo4j数据库URI')
    parser.add_argument('--user', default='neo4j', help='用户名')
    parser.add_argument('--password', default='angelinvestment', help='密码')
    parser.add_argument('--database', default='angelinvestment', help='数据库名称')
    parser.add_argument('--investor', help='查询特定投资机构')
    parser.add_argument('--company', help='查询特定公司')
    
    args = parser.parse_args()
    
    print("Neo4j查询示例工具")
    print("=" * 50)
    
    # 连接数据库
    graph = connect_to_neo4j(args.uri, args.user, args.password, args.database)
    if not graph:
        return
    
    # 执行查询
    query_statistics(graph)
    query_investor_details(graph, args.investor)
    query_company_details(graph, args.company)
    query_investment_relationships(graph)
    query_investment_rounds(graph)
    
    print("\n查询完成！")
    print("\n在Neo4j Browser中，您可以:")
    print("1. 点击任意节点查看其详细属性")
    print("2. 使用以下查询语句:")
    print("   MATCH (n:AngelInvestor) RETURN n LIMIT 25")
    print("   MATCH (n:StartupCompany) RETURN n LIMIT 25")
    print("   MATCH (investor)-[r:INVESTS]->(company) RETURN investor, r, company LIMIT 25")

if __name__ == "__main__":
    main()
