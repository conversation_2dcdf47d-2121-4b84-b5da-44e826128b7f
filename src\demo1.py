import json
import os
import time
import re
import random
import concurrent.futures
from urllib.parse import quote
import requests
from bs4 import BeautifulSoup
from collections import defaultdict

# 设置请求头，模拟浏览器访问
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Connection': 'keep-alive',
}

# 百科URL模板
ENCYCLOPEDIA_URLS = {
    'baidu': "https://baike.baidu.com/item/{}",
    'wikipedia': "https://zh.wikipedia.org/wiki/{}",
    'so': "https://baike.so.com/doc/{}",
    'hudong': "https://www.baike.com/wiki/{}",
    'military': "https://military.baidu.com/item/{}",
    'finance': "https://finance.baidu.com/baike/{}"
}

# 搜索引擎URL
SEARCH_ENGINES = {
    'baidu': "https://www.baidu.com/s?wd={}",
    'bing': "https://cn.bing.com/search?q={}",
    'sogou': "https://www.sogou.com/web?query={}"
}

def load_angel_investors(file_path='models/angel_investors.json'):
    """加载投资公司数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"加载数据失败: {e}")
        return []

def fetch_page(url, retries=2):
    """通用页面获取函数，支持重试和随机User-Agent"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 11.5; rv:90.0) Gecko/20100101 Firefox/90.0'
    ]
    
    for attempt in range(retries):
        try:
            # 随机更换User-Agent
            headers = HEADERS.copy()
            headers['User-Agent'] = random.choice(user_agents)
            
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            
            # 检查是否被重定向到错误页面
            if "error.html" in response.url or "notfound" in response.url:
                raise requests.exceptions.HTTPError(f"重定向到错误页面: {response.url}")
                
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"尝试 {attempt+1}/{retries} 获取页面 {url} 时出错: {e}")
            if attempt < retries - 1:
                wait_time = random.uniform(1, 3)  # 缩短重试等待时间
                print(f"等待 {wait_time:.2f} 秒后重试")
                time.sleep(wait_time)
    return None

def search_encyclopedia(keyword, encyclopedia='baidu'):
    """在指定百科中搜索关键词"""
    encoded_keyword = quote(keyword)
    search_engine = 'baidu' if encyclopedia in ['baidu', 'hudong'] else 'bing'
    search_url = SEARCH_ENGINES[search_engine].format(f"{keyword} {encyclopedia}百科")
    
    print(f"在{encyclopedia}百科中搜索: {keyword}")
    html_content = fetch_page(search_url)
    
    if not html_content:
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 根据不同百科调整搜索结果解析逻辑
    if encyclopedia == 'baidu':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "baike.baidu.com/item" in href:
                return href
    elif encyclopedia == 'wikipedia':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "zh.wikipedia.org/wiki" in href:
                return href
    elif encyclopedia == 'so':
        for link in soup.find_all('a', href=True):
            href = link['href']
            if "baike.so.com/doc" in href:
                return href
    elif encyclopedia == 'hudong':
        for link in soup.find_all('a', href=True):
            href = link.get('href', '')
            if "www.baike.com/wiki/" in href:
                return href
    
    print(f"未在{encyclopedia}百科中找到 {keyword} 的链接")
    return None

def parse_baidu_baike(html_content):
    """解析百度百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 尝试新老两种知识盒子结构
    basic_info = soup.find('div', class_='basic-info J-basic-info') or soup.find('div', class_='basic-info')
    
    if basic_info:
        dt_elements = basic_info.find_all('dt', class_='basicInfo-item name')
        dd_elements = basic_info.find_all('dd', class_='basicInfo-item value')
        
        for dt, dd in zip(dt_elements, dd_elements):
            key = dt.get_text(strip=True).replace('\xa0', '')
            value = dd.get_text(strip=True).replace('\xa0', ' ')
            result[key] = value
    
    # 提取摘要
    summary = soup.find('div', class_='lemma-summary') or soup.find('div', class_='J-summary')
    if summary:
        result['摘要'] = summary.get_text(strip=True)
    
    return result

def parse_wikipedia(html_content):
    """解析维基百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 解析信息框
    infobox = soup.find('table', class_='infobox')
    if infobox:
        rows = infobox.find_all('tr')
        for row in rows:
            th = row.find('th')
            td = row.find('td')
            if th and td:
                key = th.get_text(strip=True)
                value = td.get_text(strip=True)
                result[key] = value
    
    # 提取摘要
    summary = soup.find('div', class_='mw-parser-output')
    if summary and summary.p:
        result['摘要'] = summary.p.get_text(strip=True)
    
    return result

def parse_so_baike(html_content):
    """解析360百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 解析信息栏
    info_box = soup.find('div', class_='basic-info')
    if info_box:
        items = info_box.find_all('div', class_='item')
        for item in items:
            name = item.find('div', class_='name')
            value = item.find('div', class_='value')
            if name and value:
                key = name.get_text(strip=True)
                value_text = value.get_text(strip=True)
                result[key] = value_text
    
    # 提取摘要
    summary = soup.find('div', class_='summary')
    if summary:
        result['摘要'] = summary.get_text(strip=True)
    
    return result

def parse_hudong_baike(html_content):
    """解析互动百科内容"""
    if not html_content:
        return {}
    
    soup = BeautifulSoup(html_content, 'html.parser')
    result = {}
    
    # 解析信息框
    infobox = soup.find('div', class_='baseInfo')
    if infobox:
        items = infobox.find_all('div', class_='baseInfoItem')
        for item in items:
            key_element = item.find('div', class_='baseInfoKey')
            value_element = item.find('div', class_='baseInfoValue')
            if key_element and value_element:
                key = key_element.get_text(strip=True).replace('：', '')
                value = value_element.get_text(strip=True)
                result[key] = value
    
    # 提取摘要
    summary = soup.find('div', class_='summary')
    if summary:
        result['摘要'] = summary.get_text(strip=True)
    
    return result

def extract_company_info(knowledge_box, source):
    """从知识盒子中提取公司信息，适配不同百科"""
    field_mappings = {
        'baidu': {
            '成立时间': ['成立时间'],
            '公司类型': ['公司类型'],
            '总部地点': ['总部地点'],
            '创始人': ['创始人'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本'],
            '经营范围': ['经营范围'],
            '业务领域': ['业务领域', '业务范围'],
            '投资领域': ['投资领域']
        },
        'wikipedia': {
            '成立时间': ['成立时间', '成立'],
            '公司类型': ['公司类型', '企业类型'],
            '总部地点': ['总部地点', '总部所在地'],
            '创始人': ['创始人', '创立者'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本', '实收资本'],
            '经营范围': ['经营范围', '业务范围'],
            '业务领域': ['业务领域', '产业'],
            '投资领域': ['投资领域']
        },
        'so': {
            '成立时间': ['成立时间'],
            '公司类型': ['公司类型'],
            '总部地点': ['总部地点'],
            '创始人': ['创始人'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本'],
            '经营范围': ['经营范围'],
            '业务领域': ['业务领域', '业务范围'],
            '投资领域': ['投资领域']
        },
        'hudong': {
            '成立时间': ['成立时间'],
            '公司类型': ['公司类型'],
            '总部地点': ['总部地点'],
            '创始人': ['创始人'],
            '董事长': ['董事长'],
            '法定代表人': ['法定代表人'],
            '注册资本': ['注册资本'],
            '经营范围': ['经营范围'],
            '业务领域': ['主营业务', '业务范围'],
            '投资领域': ['投资方向', '投资领域']
        }
    }
    
    company_info = {}
    mapping = field_mappings.get(source, {})
    
    for target, sources in mapping.items():
        for source_key in sources:
            if source_key in knowledge_box:
                company_info[target] = knowledge_box[source_key]
                break
    
    # 提取摘要
    if '摘要' in knowledge_box:
        company_info['摘要'] = knowledge_box['摘要']
    
    return {k: v for k, v in company_info.items() if v}

def fetch_encyclopedia_data(company_name, source, parser):
    """获取并解析单个百科数据"""
    print(f"尝试从 {source} 百科获取 {company_name} 的信息")
    encoded_name = quote(company_name)
    url_template = ENCYCLOPEDIA_URLS.get(source, '')
    
    # 直接访问
    direct_url = url_template.format(encoded_name) if url_template else None
    html_content = fetch_page(direct_url) if direct_url else None
    
    # 如果直接访问失败，尝试搜索
    if not html_content or "未找到" in html_content or "不存在" in html_content:
        print(f"直接访问失败，尝试在 {source} 百科中搜索 {company_name}")
        search_url = search_encyclopedia(company_name, source)
        if search_url:
            html_content = fetch_page(search_url)
    
    if not html_content:
        return None
    
    knowledge_box = parser(html_content)
    if not knowledge_box:
        return None
    
    company_info = extract_company_info(knowledge_box, source)
    return {
        'source': source,
        'url': direct_url or search_url,
        'raw_data': knowledge_box,
        'info': company_info
    } if company_info else None

def crawl_company_info(company_name):
    """
    并行从多个百科获取公司信息
    优先顺序: 百度百科 -> 维基百科 -> 360百科 -> 互动百科
    """
    print(f"开始爬取 {company_name} 的信息")
    result = {
        'company_name': company_name,
        'sources': [],
        'info': {},
        'status': 'fail'
    }
    
    encyclopedias = [
        ('baidu', parse_baidu_baike),
        ('wikipedia', parse_wikipedia),
        ('so', parse_so_baike),
        ('hudong', parse_hudong_baike)
    ]
    
    # 设置多线程线程数为5
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = {}
        for source, parser in encyclopedias:
            future = executor.submit(fetch_encyclopedia_data, company_name, source, parser)
            futures[future] = source
        
        for future in concurrent.futures.as_completed(futures):
            source = futures[future]
            try:
                data = future.result()
                if data and data['info']:
                    result['sources'].append({
                        'source': data['source'],
                        'url': data['url'],
                        'raw_data': data['raw_data']
                    })
                    result['info'].update(data['info'])
                    result['status'] = 'success'
                    print(f"从 {source} 百科成功获取到 {company_name} 的信息")
            except Exception as e:
                print(f"获取 {source} 数据时发生错误: {str(e)}")
    
    return result

def main():
    angel_investors = load_angel_investors()
    if not angel_investors:
        return
    
    print(f"开始处理 {len(angel_investors)} 家投资公司")
    print(f"已加载 {len(angel_investors)} 家投资公司数据")
    
    os.makedirs('data/multi_encyclopedia_data', exist_ok=True)
    
    success_count = 0
    fail_count = 0
    
    # 移除了数量限制，处理所有公司
    for i, investor in enumerate(angel_investors):
        company_name = investor['name']
        print(f"\n正在处理 ({i+1}/{len(angel_investors)}): {company_name}")
        
        result = crawl_company_info(company_name)
        investor['encyclopedia'] = result
        
        company_file = os.path.join('data/multi_encyclopedia_data', f"{investor['id']}.json")
        with open(company_file, 'w', encoding='utf-8') as f:
            json.dump(investor, f, ensure_ascii=False, indent=2)
        
        if result['status'] == 'success':
            sources = [s['source'] for s in result['sources']]
            print(f"成功从 {', '.join(sources)} 获取信息")
            success_count += 1
        else:
            print("未能获取有效信息")
            fail_count += 1
        
        print(f"{company_name} 处理完成，状态: {result['status']}")
        time.sleep(random.uniform(1, 3))  # 降低请求频率
    
    output_file = 'models/angel_investors_with_multi_encyclopedia.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(angel_investors, f, ensure_ascii=False, indent=2)
    
    print(f"\n处理完成，结果保存到: {output_file}")
    print(f"成功: {success_count} 家, 失败: {fail_count} 家, 成功率: {success_count/len(angel_investors):.2%}")
    
    analyze_investment_fields(angel_investors)

def analyze_investment_fields(investors):
    """分析投资领域分布"""
    field_stats = defaultdict(int)
    
    for investor in investors:
        if 'encyclopedia' in investor and investor['encyclopedia']['status'] == 'success':
            info = investor['encyclopedia']['info']
            fields = info.get('投资领域', '') or info.get('业务领域', '') or info.get('经营范围', '')
            
            if fields:
                for field in re.split(r'[,，;；、]', fields):
                    field = field.strip()
                    if field:
                        field_stats[field] += 1
    
    print("\n投资领域分布TOP15:")
    for field, count in sorted(field_stats.items(), key=lambda x: x[1], reverse=True)[:15]:
        print(f"  {field}: {count}家公司")

if __name__ == "__main__":
    main()