#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import csv
import matplotlib.pyplot as plt
from py2neo import Graph
import pandas as pd
import numpy as np
from collections import defaultdict

# 连接Neo4j数据库
def connect_to_neo4j(uri="bolt://localhost:7687", user="neo4j", password="password"):
    """连接Neo4j数据库"""
    try:
        graph = Graph(uri, auth=(user, password))
        print("成功连接到Neo4j数据库")
        return graph
    except Exception as e:
        print(f"连接Neo4j数据库失败: {e}")
        return None

# 分析节点数量
def analyze_node_count(graph):
    """分析节点数量"""
    if not graph:
        return None
    
    # 查询各类型节点数量
    query = """
    MATCH (n)
    RETURN labels(n) AS labels, count(*) AS count
    ORDER BY count DESC
    """
    
    result = graph.run(query).data()
    
    # 处理结果
    node_counts = {}
    for record in result:
        label = record['labels'][0] if record['labels'] else 'Unknown'
        count = record['count']
        node_counts[label] = count
    
    return node_counts

# 分析关系数量
def analyze_relationship_count(graph):
    """分析关系数量"""
    if not graph:
        return None
    
    # 查询各类型关系数量
    query = """
    MATCH ()-[r]->()
    RETURN type(r) AS type, count(*) AS count
    ORDER BY count DESC
    """
    
    result = graph.run(query).data()
    
    # 处理结果
    relationship_counts = {}
    for record in result:
        rel_type = record['type']
        count = record['count']
        relationship_counts[rel_type] = count
    
    return relationship_counts

# 分析投资轮次分布
def analyze_investment_rounds(graph):
    """分析投资轮次分布"""
    if not graph:
        return None
    
    # 查询投资轮次分布
    query = """
    MATCH (e:InvestmentEvent)-[:HAS_ROUND]->(r)
    RETURN r.name AS round, count(*) AS count
    ORDER BY count DESC
    """
    
    result = graph.run(query).data()
    
    # 处理结果
    round_counts = {}
    for record in result:
        round_name = record['round']
        count = record['count']
        round_counts[round_name] = count
    
    return round_counts

# 分析投资机构投资活跃度
def analyze_investor_activity(graph):
    """分析投资机构投资活跃度"""
    if not graph:
        return None
    
    # 查询投资机构投资活跃度
    query = """
    MATCH (i)-[:INVESTS]->(c)
    WHERE i:AngelInvestor OR i:VentureCapital
    RETURN i.name AS investor, count(*) AS investments
    ORDER BY investments DESC
    LIMIT 20
    """
    
    result = graph.run(query).data()
    
    # 处理结果
    investor_activity = {}
    for record in result:
        investor = record['investor']
        investments = record['investments']
        investor_activity[investor] = investments
    
    return investor_activity

# 分析被投资公司受欢迎度
def analyze_company_popularity(graph):
    """分析被投资公司受欢迎度"""
    if not graph:
        return None
    
    # 查询被投资公司受欢迎度
    query = """
    MATCH (i)-[:INVESTS]->(c:StartupCompany)
    RETURN c.name AS company, count(*) AS investors
    ORDER BY investors DESC
    LIMIT 20
    """
    
    result = graph.run(query).data()
    
    # 处理结果
    company_popularity = {}
    for record in result:
        company = record['company']
        investors = record['investors']
        company_popularity[company] = investors
    
    return company_popularity

# 可视化节点数量
def visualize_node_count(node_counts, output_dir):
    """可视化节点数量"""
    if not node_counts:
        return
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 准备数据
    labels = list(node_counts.keys())
    counts = list(node_counts.values())
    
    # 创建柱状图
    plt.figure(figsize=(12, 6))
    plt.bar(labels, counts, color='skyblue')
    plt.xlabel('节点类型')
    plt.ylabel('数量')
    plt.title('Neo4j数据库中各类型节点数量')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, 'node_count.png')
    plt.savefig(output_file)
    plt.close()
    
    print(f"节点数量可视化已保存到: {output_file}")

# 可视化关系数量
def visualize_relationship_count(relationship_counts, output_dir):
    """可视化关系数量"""
    if not relationship_counts:
        return
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 准备数据
    types = list(relationship_counts.keys())
    counts = list(relationship_counts.values())
    
    # 创建柱状图
    plt.figure(figsize=(12, 6))
    plt.bar(types, counts, color='lightgreen')
    plt.xlabel('关系类型')
    plt.ylabel('数量')
    plt.title('Neo4j数据库中各类型关系数量')
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, 'relationship_count.png')
    plt.savefig(output_file)
    plt.close()
    
    print(f"关系数量可视化已保存到: {output_file}")

# 可视化投资轮次分布
def visualize_investment_rounds(round_counts, output_dir):
    """可视化投资轮次分布"""
    if not round_counts:
        return
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 准备数据
    rounds = list(round_counts.keys())
    counts = list(round_counts.values())
    
    # 创建饼图
    plt.figure(figsize=(10, 8))
    plt.pie(counts, labels=rounds, autopct='%1.1f%%', startangle=90, shadow=True)
    plt.axis('equal')
    plt.title('投资轮次分布')
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, 'investment_rounds.png')
    plt.savefig(output_file)
    plt.close()
    
    print(f"投资轮次分布可视化已保存到: {output_file}")

# 可视化投资机构投资活跃度
def visualize_investor_activity(investor_activity, output_dir):
    """可视化投资机构投资活跃度"""
    if not investor_activity:
        return
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 准备数据
    investors = list(investor_activity.keys())
    investments = list(investor_activity.values())
    
    # 创建水平条形图
    plt.figure(figsize=(12, 8))
    plt.barh(investors, investments, color='coral')
    plt.xlabel('投资次数')
    plt.ylabel('投资机构')
    plt.title('投资机构投资活跃度 (Top 20)')
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, 'investor_activity.png')
    plt.savefig(output_file)
    plt.close()
    
    print(f"投资机构投资活跃度可视化已保存到: {output_file}")

# 可视化被投资公司受欢迎度
def visualize_company_popularity(company_popularity, output_dir):
    """可视化被投资公司受欢迎度"""
    if not company_popularity:
        return
    
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 准备数据
    companies = list(company_popularity.keys())
    investors = list(company_popularity.values())
    
    # 创建水平条形图
    plt.figure(figsize=(12, 8))
    plt.barh(companies, investors, color='lightblue')
    plt.xlabel('投资机构数量')
    plt.ylabel('被投资公司')
    plt.title('被投资公司受欢迎度 (Top 20)')
    plt.tight_layout()
    
    # 保存图表
    output_file = os.path.join(output_dir, 'company_popularity.png')
    plt.savefig(output_file)
    plt.close()
    
    print(f"被投资公司受欢迎度可视化已保存到: {output_file}")

# 生成分析报告
def generate_analysis_report(node_counts, relationship_counts, round_counts, investor_activity, company_popularity, output_dir):
    """生成分析报告"""
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建报告文件
    report_file = os.path.join(output_dir, 'analysis_report.md')
    
    # 报告内容
    report_content = f"""# 中国天使投资知识图谱数据分析报告

## 1. 数据概览

### 1.1 节点数量

| 节点类型 | 数量 |
|---------|------|
"""
    
    # 添加节点数量
    if node_counts:
        for label, count in node_counts.items():
            report_content += f"| {label} | {count} |\n"
    
    report_content += """
### 1.2 关系数量

| 关系类型 | 数量 |
|---------|------|
"""
    
    # 添加关系数量
    if relationship_counts:
        for rel_type, count in relationship_counts.items():
            report_content += f"| {rel_type} | {count} |\n"
    
    report_content += """
## 2. 投资轮次分析

| 轮次 | 投资事件数量 | 占比 |
|------|------------|------|
"""
    
    # 添加投资轮次分析
    if round_counts:
        total_events = sum(round_counts.values())
        for round_name, count in round_counts.items():
            percentage = (count / total_events) * 100 if total_events > 0 else 0
            report_content += f"| {round_name} | {count} | {percentage:.2f}% |\n"
    
    report_content += """
## 3. 投资机构活跃度分析

| 排名 | 投资机构 | 投资次数 |
|------|---------|---------|
"""
    
    # 添加投资机构活跃度分析
    if investor_activity:
        for i, (investor, investments) in enumerate(investor_activity.items(), 1):
            report_content += f"| {i} | {investor} | {investments} |\n"
    
    report_content += """
## 4. 被投资公司受欢迎度分析

| 排名 | 被投资公司 | 投资机构数量 |
|------|----------|-------------|
"""
    
    # 添加被投资公司受欢迎度分析
    if company_popularity:
        for i, (company, investors) in enumerate(company_popularity.items(), 1):
            report_content += f"| {i} | {company} | {investors} |\n"
    
    report_content += """
## 5. 结论与建议

1. **投资轮次分布**：根据分析，A轮和B轮投资占比较高，说明中国天使投资市场正在逐渐成熟，投资机构更倾向于投资已经有一定发展的创业公司。

2. **投资机构活跃度**：排名靠前的投资机构投资次数明显高于其他机构，说明中国天使投资市场存在头部效应，少数几家投资机构占据了大部分市场份额。

3. **被投资公司受欢迎度**：受欢迎度高的公司往往是行业内的领先企业或具有高增长潜力的创业公司，这些公司更容易获得多轮融资。

4. **建议**：
   - 对于创业者：寻求头部投资机构的投资可能更有利于后续融资
   - 对于投资机构：可以关注那些已经获得多家机构投资的公司，这些公司可能具有更高的成功概率
   - 对于政策制定者：可以考虑出台政策鼓励投资机构投资早期项目，促进创业生态系统的健康发展
"""
    
    # 写入报告文件
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"分析报告已生成: {report_file}")

# 主函数
def main():
    # 连接Neo4j数据库
    graph = connect_to_neo4j()
    
    if not graph:
        print("无法连接到Neo4j数据库，请检查连接参数")
        return
    
    # 输出目录
    output_dir = 'neo4j_analysis'
    
    # 分析数据
    node_counts = analyze_node_count(graph)
    relationship_counts = analyze_relationship_count(graph)
    round_counts = analyze_investment_rounds(graph)
    investor_activity = analyze_investor_activity(graph)
    company_popularity = analyze_company_popularity(graph)
    
    # 可视化结果
    visualize_node_count(node_counts, output_dir)
    visualize_relationship_count(relationship_counts, output_dir)
    visualize_investment_rounds(round_counts, output_dir)
    visualize_investor_activity(investor_activity, output_dir)
    visualize_company_popularity(company_popularity, output_dir)
    
    # 生成分析报告
    generate_analysis_report(node_counts, relationship_counts, round_counts, investor_activity, company_popularity, output_dir)
    
    print("数据分析完成！")

if __name__ == "__main__":
    main()
