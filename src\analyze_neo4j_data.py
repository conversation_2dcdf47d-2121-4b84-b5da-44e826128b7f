#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j数据分析脚本
分析知识图谱中的数据分布和统计信息
"""

import argparse
import logging
from neo4j_connector import Neo4jConnector
import json

logger = logging.getLogger(__name__)

class Neo4jDataAnalyzer:
    def __init__(self, neo4j_connector):
        self.neo4j = neo4j_connector
    
    def get_basic_stats(self):
        """获取基本统计信息"""
        stats = self.neo4j.get_database_stats()
        
        print("=== 基本统计信息 ===")
        print(f"节点统计:")
        for label, count in stats['nodes'].items():
            print(f"  {label}: {count} 个")
        
        print(f"\n关系统计:")
        for rel_type, count in stats['relationships'].items():
            print(f"  {rel_type}: {count} 个")
        
        return stats
    
    def analyze_investors(self):
        """分析投资机构"""
        print("\n=== 投资机构分析 ===")
        
        # 天使投资机构 vs 风险投资机构
        angel_query = "MATCH (n:AngelInvestor) RETURN count(n) as count"
        vc_query = "MATCH (n:VentureCapital) RETURN count(n) as count"
        
        angel_count = self.neo4j.execute_query(angel_query)[0]['count']
        vc_count = self.neo4j.execute_query(vc_query)[0]['count']
        
        print(f"天使投资机构: {angel_count} 家")
        print(f"风险投资机构: {vc_count} 家")
        print(f"总计: {angel_count + vc_count} 家")
        
        # 按地区分布
        location_query = """
        MATCH (investor)-[:LOCATED_IN]->(location:Location)
        RETURN location.name as 地区, count(investor) as 数量
        ORDER BY 数量 DESC
        LIMIT 10
        """
        
        location_results = self.neo4j.execute_query(location_query)
        if location_results:
            print(f"\n地区分布 (前10名):")
            for result in location_results:
                print(f"  {result['地区']}: {result['数量']} 家")
        
        # 按行业分布
        industry_query = """
        MATCH (investor)-[:BELONGS_TO_INDUSTRY]->(industry:Industry)
        RETURN industry.name as 行业, count(investor) as 数量
        ORDER BY 数量 DESC
        LIMIT 10
        """
        
        industry_results = self.neo4j.execute_query(industry_query)
        if industry_results:
            print(f"\n行业分布 (前10名):")
            for result in industry_results:
                print(f"  {result['行业']}: {result['数量']} 家")
    
    def analyze_companies(self):
        """分析创业公司"""
        print("\n=== 创业公司分析 ===")
        
        # 总数统计
        company_query = "MATCH (n:Company) RETURN count(n) as count"
        company_count = self.neo4j.execute_query(company_query)[0]['count']
        print(f"创业公司总数: {company_count} 家")
        
        # 按行业分布
        industry_query = """
        MATCH (company:Company)-[:BELONGS_TO_INDUSTRY]->(industry:Industry)
        RETURN industry.name as 行业, count(company) as 数量
        ORDER BY 数量 DESC
        LIMIT 10
        """
        
        industry_results = self.neo4j.execute_query(industry_query)
        if industry_results:
            print(f"\n行业分布 (前10名):")
            for result in industry_results:
                print(f"  {result['行业']}: {result['数量']} 家")
    
    def analyze_investments(self):
        """分析投资关系"""
        print("\n=== 投资关系分析 ===")
        
        # 投资关系总数
        invest_query = "MATCH ()-[r:INVESTS]->() RETURN count(r) as count"
        invest_count = self.neo4j.execute_query(invest_query)[0]['count']
        print(f"投资关系总数: {invest_count} 个")
        
        # 按轮次分布
        round_query = """
        MATCH ()-[r:INVESTS]->()
        WHERE r.roundType IS NOT NULL AND r.roundType <> ''
        RETURN r.roundType as 轮次, count(r) as 数量
        ORDER BY 数量 DESC
        LIMIT 10
        """
        
        round_results = self.neo4j.execute_query(round_query)
        if round_results:
            print(f"\n投资轮次分布 (前10名):")
            for result in round_results:
                print(f"  {result['轮次']}: {result['数量']} 次")
        
        # 最活跃的投资机构
        active_query = """
        MATCH (investor)-[r:INVESTS]->(company)
        RETURN investor.name as 投资机构, count(r) as 投资次数
        ORDER BY 投资次数 DESC
        LIMIT 10
        """
        
        active_results = self.neo4j.execute_query(active_query)
        if active_results:
            print(f"\n最活跃投资机构 (前10名):")
            for result in active_results:
                print(f"  {result['投资机构']}: {result['投资次数']} 次")
    
    def analyze_people(self):
        """分析人员信息"""
        print("\n=== 人员信息分析 ===")
        
        # 人员总数
        person_query = "MATCH (n:Person) RETURN count(n) as count"
        person_count = self.neo4j.execute_query(person_query)[0]['count']
        print(f"人员总数: {person_count} 人")
        
        # 按角色分布
        role_query = """
        MATCH (person:Person)
        WHERE person.role IS NOT NULL AND person.role <> ''
        RETURN person.role as 角色, count(person) as 数量
        ORDER BY 数量 DESC
        """
        
        role_results = self.neo4j.execute_query(role_query)
        if role_results:
            print(f"\n角色分布:")
            for result in role_results:
                print(f"  {result['角色']}: {result['数量']} 人")
    
    def analyze_network_structure(self):
        """分析网络结构"""
        print("\n=== 网络结构分析 ===")
        
        # 计算度中心性（连接数最多的节点）
        degree_query = """
        MATCH (n)
        WITH n, size((n)--()) as degree
        WHERE degree > 0
        RETURN labels(n)[0] as 节点类型, n.name as 节点名称, degree as 连接数
        ORDER BY degree DESC
        LIMIT 10
        """
        
        degree_results = self.neo4j.execute_query(degree_query)
        if degree_results:
            print(f"连接数最多的节点 (前10名):")
            for result in degree_results:
                print(f"  {result['节点名称']} ({result['节点类型']}): {result['连接数']} 个连接")
    
    def generate_report(self, output_file=None):
        """生成完整分析报告"""
        print("中国天使投资知识图谱数据分析报告")
        print("=" * 50)
        
        # 基本统计
        stats = self.get_basic_stats()
        
        # 各类分析
        self.analyze_investors()
        self.analyze_companies()
        self.analyze_investments()
        self.analyze_people()
        self.analyze_network_structure()
        
        # 保存报告
        if output_file:
            report_data = {
                'basic_stats': stats,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n分析报告已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='Neo4j数据分析工具')
    parser.add_argument('--uri', default='bolt://localhost:7687', help='Neo4j数据库URI')
    parser.add_argument('--user', default='neo4j', help='用户名')
    parser.add_argument('--password', default='angelinvestment', help='密码')
    parser.add_argument('--database', default='', help='数据库名称')
    parser.add_argument('--output', help='输出报告文件路径')
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 连接Neo4j
    neo4j = Neo4jConnector(
        uri=args.uri,
        user=args.user,
        password=args.password,
        database=args.database if args.database else None
    )
    
    if not neo4j.graph:
        logger.error("无法连接到Neo4j数据库")
        return
    
    # 创建分析器
    analyzer = Neo4jDataAnalyzer(neo4j)
    
    # 生成报告
    analyzer.generate_report(args.output)

if __name__ == "__main__":
    import time
    main()
