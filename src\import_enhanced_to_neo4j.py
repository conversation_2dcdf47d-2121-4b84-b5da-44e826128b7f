#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版Neo4j数据导入脚本
导入包含更多节点类型和关系的知识图谱
"""

import os
import csv
import argparse
import logging
from py2neo import Graph, Node, Relationship

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                reader = csv.DictReader(f)
                data = list(reader)
                logger.info(f"使用 {encoding} 编码成功读取了 {len(data)} 行数据")
                return data
        except UnicodeDecodeError:
            continue
        except Exception as e:
            logger.error(f"读取文件 {file_path} 时出错: {e}")
            continue
    
    logger.error(f"无法读取文件 {file_path}，尝试了所有编码")
    return []

def import_nodes(graph, csv_file, node_label, batch_size=1000):
    """导入节点数据"""
    logger.info(f"导入 {node_label} 节点...")
    
    if not os.path.exists(csv_file):
        logger.warning(f"文件不存在: {csv_file}")
        return 0
    
    data = read_csv_with_encoding(csv_file)
    if not data:
        return 0
    
    logger.info(f"导入 {node_label} 节点数据")
    logger.info(f"读取CSV文件: {csv_file}")
    
    # 批量创建节点
    nodes = []
    for i, row in enumerate(data):
        # 移除 :LABEL 字段，因为它不是节点属性
        node_props = {k: v for k, v in row.items() if k != ':LABEL'}
        # 移除 id:ID 字段中的 :ID 后缀
        if 'id:ID' in node_props:
            node_props['id'] = node_props.pop('id:ID')
        
        node = Node(node_label, **node_props)
        nodes.append(node)
        
        # 批量处理
        if len(nodes) >= batch_size:
            graph.create(*nodes)
            logger.info(f"已导入 {i + 1} 个 {node_label} 节点")
            nodes = []
    
    # 处理剩余节点
    if nodes:
        graph.create(*nodes)
        logger.info(f"已导入 {len(data)} 个 {node_label} 节点")
    
    logger.info(f"共导入 {len(data)} 个 {node_label} 节点")
    logger.info(f"成功导入 {len(data)} 个 {node_label} 节点")
    
    return len(data)

def import_relationships(graph, csv_file, rel_type, batch_size=1000):
    """导入关系数据"""
    logger.info(f"导入 {rel_type} 关系...")
    
    if not os.path.exists(csv_file):
        logger.warning(f"文件不存在: {csv_file}")
        return 0
    
    data = read_csv_with_encoding(csv_file)
    if not data:
        return 0
    
    logger.info(f"导入 {rel_type} 关系数据")
    logger.info(f"读取CSV文件: {csv_file}")
    
    # 检查节点是否存在
    start_ids = set(row[':START_ID'] for row in data)
    end_ids = set(row[':END_ID'] for row in data)
    all_ids = start_ids.union(end_ids)
    
    logger.info(f"检查 {len(all_ids)} 个节点是否存在...")
    
    # 批量检查节点存在性
    existing_nodes = {}
    for node_id in all_ids:
        result = graph.run("MATCH (n {id: $id}) RETURN n", id=node_id).data()
        if result:
            existing_nodes[node_id] = result[0]['n']
    
    logger.info(f"找到 {len(existing_nodes)} 个存在的节点")
    
    # 创建关系
    logger.info(f"创建 {rel_type} 关系...")
    
    relationships = []
    created_count = 0
    
    for i, row in enumerate(data):
        start_id = row[':START_ID']
        end_id = row[':END_ID']
        
        if start_id in existing_nodes and end_id in existing_nodes:
            start_node = existing_nodes[start_id]
            end_node = existing_nodes[end_id]
            
            # 移除特殊字段
            rel_props = {k: v for k, v in row.items() if not k.startswith(':') or k == ':TYPE'}
            if ':TYPE' in rel_props:
                rel_props.pop(':TYPE')
            
            rel = Relationship(start_node, rel_type, end_node, **rel_props)
            relationships.append(rel)
            created_count += 1
            
            # 批量处理
            if len(relationships) >= batch_size:
                graph.create(*relationships)
                logger.info(f"已导入 {created_count} 个关系")
                relationships = []
    
    # 处理剩余关系
    if relationships:
        graph.create(*relationships)
    
    logger.info(f"共导入 {created_count} 个 {rel_type} 关系")
    logger.info(f"成功导入 {created_count} 个 {rel_type} 关系")
    
    return created_count

def import_all_data(uri, user, password, database=None, csv_dir='neo4j_import_enhanced'):
    """导入所有数据到Neo4j"""
    logger.info("开始导入增强版数据到Neo4j")
    logger.info(f"Neo4j URI: {uri}")
    logger.info(f"Neo4j 数据库: {database}")
    logger.info(f"CSV文件目录: {csv_dir}")
    
    # 连接到Neo4j
    try:
        if database:
            full_uri = f"{uri}/{database}"
            logger.info(f"完整URI: {full_uri}")
            graph = Graph(full_uri, auth=(user, password))
        else:
            graph = Graph(uri, auth=(user, password))
        
        logger.info(f"连接到Neo4j数据库: {full_uri if database else uri}")
        
        # 测试连接
        result = graph.run("RETURN 1").data()
        logger.info(f"连接成功，测试查询返回: {result[0]['1']}")
        
    except Exception as e:
        logger.error(f"连接Neo4j失败: {e}")
        return False
    
    # 清空数据库
    logger.info("清空现有数据...")
    graph.run("MATCH (n) DETACH DELETE n")
    
    logger.info(f"开始导入所有数据，CSV文件目录: {csv_dir}")
    
    # 定义节点导入顺序和对应的标签
    node_imports = [
        ('persons.csv', 'Person'),
        ('industries.csv', 'Industry'),
        ('locations.csv', 'Location'),
        ('company_types.csv', 'CompanyType'),
        ('founding_dates.csv', 'FoundingDate'),
        ('investment_rounds.csv', 'InvestmentRound'),
        ('investors.csv', 'Investor'),
        ('companies.csv', 'Company'),
        ('investment_events.csv', 'InvestmentEvent')
    ]
    
    # 导入节点
    total_nodes = 0
    for csv_file, label in node_imports:
        file_path = os.path.join(csv_dir, csv_file)
        count = import_nodes(graph, file_path, label)
        total_nodes += count
    
    # 定义关系导入顺序
    relationship_imports = [
        ('founded_by_relationships.csv', 'FOUNDED_BY'),
        ('legal_representative_relationships.csv', 'LEGAL_REPRESENTATIVE'),
        ('located_in_relationships.csv', 'LOCATED_IN'),
        ('belongs_to_industry_relationships.csv', 'BELONGS_TO_INDUSTRY'),
        ('has_type_relationships.csv', 'HAS_TYPE'),
        ('founded_on_relationships.csv', 'FOUNDED_ON'),
        ('invests_relationships.csv', 'INVESTS'),
        ('has_investment_event_relationships.csv', 'HAS_INVESTMENT_EVENT'),
        ('has_round_relationships.csv', 'HAS_ROUND')
    ]
    
    # 导入关系
    total_relationships = 0
    for csv_file, rel_type in relationship_imports:
        file_path = os.path.join(csv_dir, csv_file)
        count = import_relationships(graph, file_path, rel_type)
        total_relationships += count
    
    logger.info(f"导入完成，共导入 {total_nodes} 个节点和 {total_relationships} 个关系")
    
    # 创建索引
    logger.info("创建索引")
    try:
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:Investor) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:Company) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:Person) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:Industry) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:Location) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:CompanyType) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:FoundingDate) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:InvestmentRound) ON (n.id)")
        graph.run("CREATE INDEX IF NOT EXISTS FOR (n:InvestmentEvent) ON (n.id)")
        logger.info("索引创建成功")
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
    
    logger.info("导入过程完成")
    logger.info("请访问 http://localhost:7474 查看导入的数据")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='导入增强版数据到Neo4j')
    parser.add_argument('--uri', default='bolt://localhost:7687', help='Neo4j数据库URI')
    parser.add_argument('--user', default='neo4j', help='用户名')
    parser.add_argument('--password', default='angelinvestment', help='密码')
    parser.add_argument('--database', default='', help='数据库名称')
    parser.add_argument('--csv-dir', default='neo4j_import_enhanced', help='CSV文件目录')
    
    args = parser.parse_args()
    
    success = import_all_data(
        args.uri, 
        args.user, 
        args.password, 
        args.database if args.database else None,
        args.csv_dir
    )
    
    if success:
        print("\n数据导入成功！")
        print("现在您可以在Neo4j Browser中查看增强版知识图谱")
        print("点击任意节点可以看到相关的详细信息节点")
    else:
        print("\n数据导入失败，请检查错误信息")

if __name__ == "__main__":
    main()
