#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import argparse
from typing import List, Dict, Any
from qa_state_machine import QAStateMachine, State, Intent, EntityType
from neo4j_connector import Neo4jConnector

class AngelInvestmentQASystem:
    """中国天使投资智能问答系统"""
    
    def __init__(self, neo4j_uri: str = "bolt://localhost:7687", neo4j_user: str = "neo4j", neo4j_password: str = "password"):
        """
        初始化问答系统
        
        Args:
            neo4j_uri: Neo4j数据库URI
            neo4j_user: Neo4j用户名
            neo4j_password: Neo4j密码
        """
        # 加载数据
        self.investor_data = self._load_investor_data()
        self.company_data = self._load_company_data()
        self.round_data = self._load_round_data()
        self.field_data = self._load_field_data()
        
        # 初始化状态机
        self.state_machine = QAStateMachine(
            self.investor_data,
            self.company_data,
            self.round_data,
            self.field_data
        )
        
        # 初始化Neo4j连接器
        self.neo4j_connector = Neo4jConnector(neo4j_uri, neo4j_user, neo4j_password)
        
        # 尝试连接Neo4j数据库
        self.db_connected = self.neo4j_connector.connect()
        if not self.db_connected:
            print("警告: 无法连接到Neo4j数据库，将使用模拟数据")
    
    def _load_investor_data(self) -> List[str]:
        """加载投资机构数据"""
        try:
            with open('data/investors.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或格式错误，返回示例数据
            return [
                "红杉资本中国", "IDG资本", "经纬中国", "真格基金", "腾讯投资",
                "高瓴资本", "创新工场", "金沙江创投", "阿里巴巴创投", "深创投"
            ]
    
    def _load_company_data(self) -> List[str]:
        """加载创业公司数据"""
        try:
            with open('data/companies.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或格式错误，返回示例数据
            return [
                "字节跳动", "美团", "小米", "滴滴出行", "京东",
                "蔚来汽车", "哔哩哔哩", "拼多多", "商汤科技", "旷视科技"
            ]
    
    def _load_round_data(self) -> List[str]:
        """加载投资轮次数据"""
        try:
            with open('data/rounds.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或格式错误，返回示例数据
            return [
                "天使轮", "种子轮", "Pre-A轮", "A轮", "A+轮",
                "B轮", "B+轮", "C轮", "D轮", "E轮",
                "F轮", "Pre-IPO", "新三板定增", "战略投资", "并购"
            ]
    
    def _load_field_data(self) -> List[str]:
        """加载投资领域数据"""
        try:
            with open('data/fields.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或格式错误，返回示例数据
            return [
                "人工智能", "大数据", "云计算", "区块链", "物联网",
                "医疗健康", "教育", "金融科技", "电子商务", "企业服务",
                "文娱", "出行", "硬件", "消费", "房产"
            ]
    
    def process_query(self, query: str) -> str:
        """
        处理用户查询
        
        Args:
            query: 用户查询文本
            
        Returns:
            系统响应
        """
        # 使用状态机处理查询
        response = self.state_machine.process(query)
        
        # 如果状态机已经生成了查询，执行数据库查询
        if self.state_machine.state == State.END and self.state_machine.context.get('query'):
            if self.db_connected:
                # 执行实际的数据库查询
                query_info = self.state_machine.context.get('query')
                result = self.neo4j_connector.execute_query(query_info)
                
                # 格式化查询结果
                response = self.neo4j_connector.format_result(result, query_info)
            else:
                # 使用模拟数据
                response = self._generate_mock_response(self.state_machine.context)
        
        return response
    
    def _generate_mock_response(self, context: Dict[str, Any]) -> str:
        """
        生成模拟响应
        
        Args:
            context: 状态机上下文
            
        Returns:
            模拟响应文本
        """
        entity_type = context.get('entity_type')
        entity_name = context.get('entity_name')
        intent = context.get('intent')
        
        if entity_type == EntityType.INVESTOR:
            if intent == Intent.INVESTOR_PORTFOLIO:
                return f"{entity_name}投资了以下公司：\n\n1. 字节跳动\n2. 美团\n3. 京东\n4. 滴滴出行\n5. 拼多多"
            elif intent == Intent.INVESTOR_ROUNDS:
                return f"{entity_name}的投资轮次分布：\n\nA轮: 30次投资 (35.3%)\nB轮: 25次投资 (29.4%)\nC轮: 15次投资 (17.6%)\n天使轮: 10次投资 (11.8%)\nD轮: 5次投资 (5.9%)"
            elif intent == Intent.INVESTOR_FIELDS:
                return f"{entity_name}主要投资以下领域：\n\n人工智能: 20家公司\n企业服务: 15家公司\n医疗健康: 10家公司\n教育: 8家公司\n金融科技: 5家公司"
            elif intent == Intent.INVESTOR_SPECIFIC_ROUND:
                round_name = context.get('parameters', {}).get('round', '')
                return f"{entity_name}在{round_name}投资了以下公司：\n\n1. 字节跳动（投资日期：2014-05-01）\n2. 美团（投资日期：2015-01-15）\n3. 京东（投资日期：2013-11-20）"
            elif intent == Intent.INVESTOR_CO_INVESTMENT:
                co_investor = context.get('parameters', {}).get('co_investor', '')
                return f"{entity_name}和{co_investor}共同投资了以下公司：\n\n1. 字节跳动\n2. 美团\n3. 滴滴出行"
        
        elif entity_type == EntityType.COMPANY:
            if intent == Intent.COMPANY_FUNDING_HISTORY:
                return f"{entity_name}的融资历史：\n\n1. 天使轮（投资方：真格基金）\n   投资日期：2012-03-01\n   投资金额：300万美元\n\n2. A轮（投资方：红杉资本中国）\n   投资日期：2013-04-15\n   投资金额：1000万美元\n\n3. B轮（投资方：腾讯投资）\n   投资日期：2014-06-30\n   投资金额：5000万美元"
            elif intent == Intent.COMPANY_SPECIFIC_ROUND:
                round_name = context.get('parameters', {}).get('round', '')
                return f"{entity_name}的{round_name}融资情况：\n\n1. 投资方：红杉资本中国\n   投资日期：2013-04-15\n   投资金额：1000万美元\n\n2. 投资方：IDG资本\n   投资日期：2013-04-15\n   投资金额：500万美元"
            elif intent == Intent.COMPANY_TOTAL_FUNDING:
                return f"{entity_name}的融资总额为：15亿美元"
            elif intent == Intent.COMPANY_EARLY_INVESTORS:
                return f"{entity_name}的早期投资方：\n\n1. 真格基金（天使轮）\n   投资日期：2012-03-01\n\n2. 创新工场（种子轮）\n   投资日期：2011-10-15"
            elif intent == Intent.COMPANY_COMMON_INVESTORS:
                co_company = context.get('parameters', {}).get('co_company', '')
                return f"{entity_name}和{co_company}的共同投资方（3家）：\n\n1. 红杉资本中国\n2. IDG资本\n3. 腾讯投资"
        
        return "抱歉，无法生成相关响应。"
    
    def run_interactive(self):
        """运行交互式问答系统"""
        print("欢迎使用中国天使投资智能问答系统！")
        print("您可以询问关于投资机构的投资组合、投资轮次、投资领域等信息，")
        print("也可以询问创业公司的融资历史、融资轮次、早期投资方等信息。")
        print("输入'退出'或'exit'结束对话。")
        print()
        
        while True:
            query = input("请输入您的问题: ")
            if query.lower() in ['退出', 'exit', 'quit']:
                print("谢谢使用，再见！")
                break
            
            response = self.process_query(query)
            print("\n" + response + "\n")
    
    def run_demo(self, scenario: int = 1):
        """
        运行演示
        
        Args:
            scenario: 演示场景编号（1或2）
        """
        if scenario == 1:
            self._run_investor_portfolio_demo()
        elif scenario == 2:
            self._run_company_funding_history_demo()
        else:
            print(f"未知的演示场景: {scenario}")
    
    def _run_investor_portfolio_demo(self):
        """运行投资机构投资组合分析演示"""
        print("演示场景1: 投资机构投资组合分析")
        print("=" * 50)
        
        # 演示查询1: 投资组合
        query1 = "红杉资本中国投资了哪些公司？"
        print(f"用户: {query1}")
        response1 = self.process_query(query1)
        print(f"系统: {response1}\n")
        
        # 演示查询2: 投资轮次
        query2 = "红杉资本中国在哪些轮次投资最活跃？"
        print(f"用户: {query2}")
        response2 = self.process_query(query2)
        print(f"系统: {response2}\n")
        
        # 演示查询3: 特定轮次
        query3 = "红杉资本中国的A轮投资有哪些公司？"
        print(f"用户: {query3}")
        response3 = self.process_query(query3)
        print(f"系统: {response3}\n")
        
        # 演示查询4: 共同投资
        query4 = "红杉资本中国和IDG资本有哪些共同投资的公司？"
        print(f"用户: {query4}")
        response4 = self.process_query(query4)
        print(f"系统: {response4}\n")
        
        print("演示结束")
    
    def _run_company_funding_history_demo(self):
        """运行创业公司融资路径分析演示"""
        print("演示场景2: 创业公司融资路径分析")
        print("=" * 50)
        
        # 演示查询1: 融资历史
        query1 = "字节跳动的融资历史是怎样的？"
        print(f"用户: {query1}")
        response1 = self.process_query(query1)
        print(f"系统: {response1}\n")
        
        # 演示查询2: 特定轮次融资
        query2 = "字节跳动A轮融资的投资方是谁？"
        print(f"用户: {query2}")
        response2 = self.process_query(query2)
        print(f"系统: {response2}\n")
        
        # 演示查询3: 早期投资方
        query3 = "字节跳动最早的投资方是谁？"
        print(f"用户: {query3}")
        response3 = self.process_query(query3)
        print(f"系统: {response3}\n")
        
        # 演示查询4: 共同投资方
        query4 = "字节跳动和美团有哪些共同的投资方？"
        print(f"用户: {query4}")
        response4 = self.process_query(query4)
        print(f"系统: {response4}\n")
        
        print("演示结束")

def main():
    parser = argparse.ArgumentParser(description='中国天使投资智能问答系统')
    parser.add_argument('--uri', default='bolt://localhost:7687', help='Neo4j数据库URI')
    parser.add_argument('--user', default='neo4j', help='Neo4j用户名')
    parser.add_argument('--password', default='password', help='Neo4j密码')
    parser.add_argument('--demo', type=int, choices=[0, 1, 2], default=0, help='运行演示（0:交互式, 1:投资机构演示, 2:创业公司演示）')
    
    args = parser.parse_args()
    
    # 创建问答系统
    qa_system = AngelInvestmentQASystem(args.uri, args.user, args.password)
    
    # 根据参数运行系统
    if args.demo == 0:
        qa_system.run_interactive()
    else:
        qa_system.run_demo(args.demo)

if __name__ == "__main__":
    main()
