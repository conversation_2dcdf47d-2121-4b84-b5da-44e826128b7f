# Neo4j 知识图谱使用说明

## 1. 导入数据

使用以下脚本导入数据到Neo4j数据库：

```bash
python src/direct_import_to_neo4j.py --uri="bolt://localhost:7687" --user="neo4j" --password="angelinvestment" --database="angelinvestment"
```

## 2. 应用样式配置

### 方法一：通过Neo4j Browser界面
1. 打开Neo4j Browser (http://localhost:7474)
2. 点击右上角的设置图标（齿轮图标）
3. 选择 "Graph Visualization"
4. 将 `neo4j_style.grass` 文件的内容复制粘贴到样式编辑器中
5. 点击 "Apply" 应用样式

### 方法二：通过文件导入
1. 在Neo4j Browser中执行：`:style reset`
2. 然后执行：`:style neo4j_style.grass`

## 3. 节点类型和颜色

- **天使投资机构 (AngelInvestor)**: 红色 (#FF6B6B)
- **风险投资机构 (VentureCapital)**: 青色 (#4ECDC4)
- **创业公司 (StartupCompany)**: 蓝色 (#45B7D1)
- **投资事件 (InvestmentEvent)**: 绿色 (#96CEB4)
- **天使轮 (AngelRound)**: 黄色 (#FFEAA7)
- **种子轮 (SeedRound)**: 紫色 (#DDA0DD)
- **A轮 (RoundA)**: 浅蓝色 (#74B9FF)
- **B轮 (RoundB)**: 紫蓝色 (#A29BFE)
- **C轮 (RoundC)**: 粉色 (#FD79A8)
- **其他轮次 (InvestmentRound)**: 橙色 (#FDCB6E)

## 4. 节点属性

### 投资机构属性
- `name`: 机构名称
- `foundingDate`: 成立日期
- `description`: 详细描述
- `companyType`: 公司类型
- `industry`: 所属行业
- `businessScope`: 经营范围
- `location`: 总部地点
- `founder`: 创始人
- `legalRepresentative`: 法定代表人
- `registeredCapital`: 注册资本

### 公司属性
- `name`: 公司名称
- `foundingDate`: 成立日期
- `description`: 公司描述
- `companyType`: 公司类型
- `industry`: 所属行业
- `businessScope`: 经营范围
- `location`: 总部地点
- `founder`: 创始人
- `legalRepresentative`: 法定代表人
- `registeredCapital`: 注册资本

### 投资轮次属性
- `name`: 轮次名称
- `description`: 轮次描述
- `roundType`: 轮次类型（早期投资、成长期投资、后期投资等）
- `stage`: 投资阶段（种子期、成长期、扩张期、成熟期等）

### 投资事件属性
- `name`: 事件名称
- `investmentAmount`: 投资金额
- `investmentDate`: 投资日期
- `description`: 事件描述
- `roundType`: 轮次类型
- `stage`: 投资阶段

### 投资关系属性
- `name`: 关系名称
- `investmentAmount`: 投资金额
- `investmentDate`: 投资日期
- `roundType`: 投资轮次

## 5. 常用查询语句

### 查看所有天使投资机构
```cypher
MATCH (n:AngelInvestor) RETURN n LIMIT 25
```

### 查看特定公司的投资关系
```cypher
MATCH (investor)-[r:INVESTS]->(company:StartupCompany)
WHERE company.name CONTAINS "科技"
RETURN investor, r, company LIMIT 25
```

### 查看A轮投资
```cypher
MATCH (investor)-[r:INVESTS]->(company)
WHERE r.roundType = "A轮"
RETURN investor, r, company LIMIT 25
```

### 查看投资事件和轮次
```cypher
MATCH (investor)-[:HAS_INVESTMENT_EVENT]->(event:InvestmentEvent)-[:HAS_ROUND]->(round)
RETURN investor, event, round LIMIT 25
```

### 按行业分析投资分布
```cypher
MATCH (company:StartupCompany)
WHERE company.industry IS NOT NULL AND company.industry <> ""
RETURN company.industry as 行业, count(*) as 公司数量
ORDER BY 公司数量 DESC
```

### 按地区分析投资分布
```cypher
MATCH (company:StartupCompany)
WHERE company.location IS NOT NULL AND company.location <> ""
RETURN company.location as 地区, count(*) as 公司数量
ORDER BY 公司数量 DESC
```

### 查看投资最活跃的机构
```cypher
MATCH (investor)-[r:INVESTS]->(company)
RETURN investor.name as 投资机构, count(r) as 投资次数
ORDER BY 投资次数 DESC
LIMIT 10
```

## 6. 查看节点详细信息

点击任意节点，在右侧面板中可以查看该节点的所有属性信息，包括：
- 基本信息（名称、成立日期等）
- 公司信息（类型、行业、经营范围等）
- 地理信息（总部地点）
- 人员信息（创始人、法定代表人）
- 财务信息（注册资本）

## 7. 图谱交互

- **缩放**: 使用鼠标滚轮或触控板缩放图谱
- **拖拽**: 点击并拖拽节点来重新排列图谱
- **选择**: 点击节点或边来查看详细信息
- **展开**: 双击节点来展开相关的连接节点
- **筛选**: 使用左侧面板的标签和关系类型来筛选显示的内容

## 8. 性能优化建议

- 使用 `LIMIT` 子句限制返回结果数量
- 在查询中使用索引字段（如 `name`）
- 避免返回过多节点，建议每次查询不超过100个节点
- 使用 `WHERE` 子句进行条件筛选

## 9. 故障排除

如果图谱显示为灰色节点：
1. 确保已正确应用样式配置
2. 检查节点标签是否正确
3. 尝试刷新浏览器页面
4. 重新导入样式文件

如果无法查看节点属性：
1. 确保数据已正确导入
2. 检查CSV文件中的属性字段
3. 验证数据库连接是否正常
