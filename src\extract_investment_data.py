#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
import csv
from collections import defaultdict

def load_json_data(file_path):
    """
    加载JSON数据文件，处理可能的编码问题

    Args:
        file_path: JSON文件路径

    Returns:
        解析后的JSON数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except UnicodeDecodeError:
        # 尝试使用不同的编码
        with open(file_path, 'r', encoding='gbk') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"加载JSON文件时出错: {e}")
        # 尝试手动修复编码问题
        with open(file_path, 'rb') as f:
            content = f.read()
        # 尝试检测编码
        try:
            content = content.decode('utf-8')
        except UnicodeDecodeError:
            try:
                content = content.decode('gbk')
            except UnicodeDecodeError:
                # 如果还是失败，尝试忽略错误
                content = content.decode('utf-8', errors='ignore')

        # 修复可能的JSON格式问题
        content = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', content)
        return json.loads(content)

def clean_string(s):
    """
    清理字符串，确保CSV格式正确
    
    Args:
        s: 输入字符串
        
    Returns:
        清理后的字符串
    """
    if s is None:
        return ""
    return str(s).replace('"', '""').replace('\n', ' ').replace('\r', ' ').strip()

def extract_investment_data(kg_file, processed_file, output_dir):
    """
    从两个JSON文件中提取投资数据，并解决重复问题
    
    Args:
        kg_file: invest-on-invent-KG.json文件路径
        processed_file: angel_investors_processed.json文件路径
        output_dir: 输出目录
        
    Returns:
        生成的CSV文件路径列表
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建目录: {output_dir}")
    
    # 加载数据
    print(f"正在解析KG数据文件: {kg_file}")
    kg_data = load_json_data(kg_file)
    
    print(f"正在解析处理后的投资者数据文件: {processed_file}")
    processed_data = load_json_data(processed_file)
    
    # 检查数据结构
    if '@graph' not in kg_data:
        print(f"KG数据结构不符合预期，找不到@graph字段")
        return []
    
    if not isinstance(processed_data, list):
        print(f"处理后的投资者数据结构不符合预期，应为列表，实际为: {type(processed_data)}")
        return []
    
    # 提取公司信息
    print("正在提取公司信息...")
    companies = {}  # 公司ID到公司信息的映射
    
    # 从KG数据中提取公司信息
    for node in kg_data['@graph']:
        if '@type' in node and node['@type'] == 'company' and '@id' in node:
            company_id = node['@id']
            companies[company_id] = {
                'id': company_id,
                'name': node.get('name', f"公司{company_id}"),
                'description': node.get('description', ""),
                'foundingDate': node.get('foundingDate', "")
            }
    
    # 提取投资关系
    print("正在提取投资关系...")
    investments = []  # 投资关系列表
    
    # 从KG数据中提取投资关系
    for node in kg_data['@graph']:
        if '@type' in node and node['@type'] == 'investor' and 'relationship' in node and 'investCompany' in node['relationship']:
            investor_id = node.get('@id', "")
            investor_name = node.get('name', f"投资者{investor_id}")
            
            # 处理投资的公司
            for company in node['relationship']['investCompany']:
                if '@type' in company and company['@type'] == 'company' and '@id' in company:
                    company_id = company['@id']
                    round_name = company.get('round', "未知轮次")
                    date = company.get('date', "")
                    
                    # 添加到投资关系列表
                    investments.append({
                        'investor_id': investor_id,
                        'investor_name': investor_name,
                        'company_id': company_id,
                        'company_name': companies.get(company_id, {}).get('name', f"公司{company_id}"),
                        'round': round_name,
                        'date': date
                    })
    
    # 去重投资关系
    print("正在去重投资关系...")
    unique_investments = {}  # 使用(investor_id, company_id, round)作为键进行去重
    
    for inv in investments:
        key = (inv['investor_id'], inv['company_id'], inv['round'])
        if key not in unique_investments:
            unique_investments[key] = inv
    
    # 将去重后的投资关系转换为列表
    unique_investments_list = list(unique_investments.values())
    
    print(f"共找到 {len(companies)} 家公司和 {len(unique_investments_list)} 条投资关系")
    
    # 创建CSV文件
    company_file = os.path.join(output_dir, 'company.csv')
    investment_file = os.path.join(output_dir, 'investment.csv')
    
    # 写入公司数据
    print(f"正在写入公司数据到 {company_file}...")
    with open(company_file, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=['id', 'name', 'description', 'foundingDate'])
        writer.writeheader()
        for company in companies.values():
            writer.writerow({
                'id': company['id'],
                'name': clean_string(company['name']),
                'description': clean_string(company['description']),
                'foundingDate': clean_string(company['foundingDate'])
            })
    
    # 写入投资关系数据
    print(f"正在写入投资关系数据到 {investment_file}...")
    with open(investment_file, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=['investor_id', 'investor_name', 'company_id', 'company_name', 'round', 'date'])
        writer.writeheader()
        for inv in unique_investments_list:
            writer.writerow({
                'investor_id': inv['investor_id'],
                'investor_name': clean_string(inv['investor_name']),
                'company_id': inv['company_id'],
                'company_name': clean_string(inv['company_name']),
                'round': clean_string(inv['round']),
                'date': clean_string(inv['date'])
            })
    
    # 保存去重后的投资关系为JSON文件
    unique_investments_file = os.path.join(output_dir, 'unique_investments.json')
    print(f"正在保存去重后的投资关系到 {unique_investments_file}...")
    with open(unique_investments_file, 'w', encoding='utf-8') as f:
        json.dump(unique_investments_list, f, ensure_ascii=False, indent=2)
    
    return [company_file, investment_file, unique_investments_file]

def main():
    # 数据文件路径
    kg_file = os.path.join('data', 'invest-on-invent-KG.json')
    processed_file = os.path.join('models', 'angel_investors_processed.json')
    output_dir = os.path.join('data', 'extracted')
    
    # 提取投资数据
    files = extract_investment_data(kg_file, processed_file, output_dir)
    
    # 输出结果
    if files:
        print("\n已生成以下文件:")
        for file_path in files:
            print(f"- {file_path}")
    else:
        print("\n提取数据失败，请检查输入文件。")

if __name__ == "__main__":
    main()
