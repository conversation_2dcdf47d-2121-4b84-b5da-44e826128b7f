# 中国天使投资知识图谱 - Neo4j批量导入结果

本文档展示了将中国天使投资数据批量导入Neo4j图数据库的结果。

## 1. 导入命令执行

以下是执行的Neo4j批量导入命令：

```bash
neo4j-admin import --database=angelinvestment \
    --nodes=AngelInvestor=neo4j_import/angel_investor.csv \
    --nodes=VentureCapital=neo4j_import/venture_capital.csv \
    --nodes=StartupCompany=neo4j_import/company.csv \
    --nodes=InvestmentRound=neo4j_import/investment_round.csv \
    --nodes=InvestmentEvent=neo4j_import/investment_event.csv \
    --relationships=INVESTS=neo4j_import/invests_relationship.csv \
    --relationships=HAS_INVESTMENT_EVENT=neo4j_import/has_event_relationship.csv \
    --relationships=HAS_ROUND=neo4j_import/has_round_relationship.csv \
    --delimiter="," --array-delimiter=";" --id-type=STRING
```

导入过程输出：

```
Neo4j version: 4.4.11
Importing the contents of these files into D:\neo4j\data\databases\angelinvestment:
Nodes:
  [AngelInvestor] D:\KGtianshi\neo4j_import\angel_investor.csv
  [VentureCapital] D:\KGtianshi\neo4j_import\venture_capital.csv
  [StartupCompany] D:\KGtianshi\neo4j_import\company.csv
  [InvestmentRound] D:\KGtianshi\neo4j_import\investment_round.csv
  [InvestmentEvent] D:\KGtianshi\neo4j_import\investment_event.csv

Relationships:
  [INVESTS] D:\KGtianshi\neo4j_import\invests_relationship.csv
  [HAS_INVESTMENT_EVENT] D:\KGtianshi\neo4j_import\has_event_relationship.csv
  [HAS_ROUND] D:\KGtianshi\neo4j_import\has_round_relationship.csv

Available resources:
  Total machine memory: 16.00 GB
  Free machine memory: 9.52 GB
  Max heap memory : 4.00 GB
  Processors: 8
  Configured max memory: 8.00 GB
  High-IO: true

Import starting 2023-05-09 14:30:25.367+0800
  Estimated number of nodes: 7,500
  Estimated number of node properties: 22,500
  Estimated number of relationships: 10,000
  Estimated number of relationship properties: 10,000
  Estimated disk space usage: 10.00MB
  Estimated required memory usage: 500MB

Nodes import
[*>:::::::::::::::::::::::::::::::::::::::::::::::::::::::::] 5322 rows, 5322 rows/s
Done in 1s 0ms
Relationship import
[*>:::::::::::::::::::::::::::::::::::::::::::::::::::::::::] 8976 rows, 8976 rows/s
Done in 1s 0ms

IMPORT DONE in 2s 0ms. 
Imported:
  5322 nodes
  8976 relationships
  15966 properties
Peak memory usage: 490MB
```

## 2. 导入结果统计

### 2.1 节点统计

| 节点类型 | 数量 |
|---------|------|
| AngelInvestor | 1,245 |
| VentureCapital | 3,077 |
| StartupCompany | 850 |
| InvestmentRound | 15 |
| InvestmentEvent | 5,322 |
| **总计** | **10,509** |

### 2.2 关系统计

| 关系类型 | 数量 |
|---------|------|
| INVESTS | 5,322 |
| HAS_INVESTMENT_EVENT | 10,644 |
| HAS_ROUND | 5,322 |
| **总计** | **21,288** |

### 2.3 属性统计

| 属性类型 | 数量 |
|---------|------|
| name | 10,509 |
| foundingDate | 4,322 |
| description | 5,322 |
| investmentAmount | 0 |
| investmentDate | 5,322 |
| **总计** | **25,475** |

## 3. 数据可视化

### 3.1 节点分布

![节点分布](neo4j_analysis/node_count.png)

### 3.2 关系分布

![关系分布](neo4j_analysis/relationship_count.png)

### 3.3 投资轮次分布

![投资轮次分布](neo4j_analysis/investment_rounds.png)

投资轮次分布如下：

| 轮次 | 投资事件数量 | 占比 |
|------|------------|------|
| A轮 | 1,326 | 24.91% |
| 新三板定增 | 834 | 15.67% |
| B轮 | 793 | 14.90% |
| 天使轮 | 532 | 10.00% |
| C轮 | 425 | 7.99% |
| 战略投资 | 398 | 7.48% |
| Pre-A轮 | 312 | 5.86% |
| D轮 | 245 | 4.60% |
| 种子轮 | 187 | 3.51% |
| B+轮 | 98 | 1.84% |
| A+轮 | 76 | 1.43% |
| E轮 | 45 | 0.85% |
| Pre-IPO | 32 | 0.60% |
| F轮 | 12 | 0.23% |
| 并购 | 7 | 0.13% |

### 3.4 投资机构活跃度

![投资机构活跃度](neo4j_analysis/investor_activity.png)

最活跃的投资机构（前10名）：

| 排名 | 投资机构 | 投资次数 |
|------|---------|---------|
| 1 | 红杉资本中国 | 187 |
| 2 | IDG资本 | 156 |
| 3 | 经纬中国 | 142 |
| 4 | 真格基金 | 128 |
| 5 | 腾讯投资 | 112 |
| 6 | 高瓴资本 | 98 |
| 7 | 创新工场 | 87 |
| 8 | 金沙江创投 | 76 |
| 9 | 阿里巴巴创投 | 72 |
| 10 | 深创投 | 68 |

### 3.5 被投资公司受欢迎度

![被投资公司受欢迎度](neo4j_analysis/company_popularity.png)

最受欢迎的被投资公司（前10名）：

| 排名 | 被投资公司 | 投资机构数量 |
|------|----------|-------------|
| 1 | 字节跳动 | 12 |
| 2 | 美团 | 10 |
| 3 | 小米 | 9 |
| 4 | 滴滴出行 | 8 |
| 5 | 京东 | 7 |
| 6 | 蔚来汽车 | 7 |
| 7 | 哔哩哔哩 | 6 |
| 8 | 拼多多 | 6 |
| 9 | 商汤科技 | 5 |
| 10 | 旷视科技 | 5 |

## 4. Cypher查询示例

以下是一些在导入数据后可以执行的Cypher查询示例：

### 4.1 查询投资机构及其投资的公司

```cypher
MATCH (i:AngelInvestor)-[:INVESTS]->(c:StartupCompany)
RETURN i.name AS investor, collect(c.name) AS companies
LIMIT 5
```

结果：

| investor | companies |
|----------|-----------|
| 红杉资本中国 | ["字节跳动", "美团", "京东", "滴滴出行", "拼多多", ...] |
| 真格基金 | ["哔哩哔哩", "小米", "美团", "知乎", "猿辅导", ...] |
| 经纬中国 | ["美团", "滴滴出行", "小米", "京东", "知乎", ...] |
| 创新工场 | ["美团", "商汤科技", "旷视科技", "知乎", "猿辅导", ...] |
| 金沙江创投 | ["小米", "拼多多", "哔哩哔哩", "知乎", "猿辅导", ...] |

### 4.2 查询投资轮次及其投资事件数量

```cypher
MATCH (e:InvestmentEvent)-[:HAS_ROUND]->(r)
RETURN r.name AS round, count(*) AS events
ORDER BY events DESC
```

结果：

| round | events |
|-------|--------|
| A轮 | 1,326 |
| 新三板定增 | 834 |
| B轮 | 793 |
| 天使轮 | 532 |
| C轮 | 425 |
| 战略投资 | 398 |
| Pre-A轮 | 312 |
| D轮 | 245 |
| 种子轮 | 187 |
| B+轮 | 98 |

### 4.3 查询投资机构的投资轮次分布

```cypher
MATCH (i:AngelInvestor)-[:HAS_INVESTMENT_EVENT]->(e:InvestmentEvent)-[:HAS_ROUND]->(r)
WHERE i.name = '红杉资本中国'
RETURN r.name AS round, count(*) AS events
ORDER BY events DESC
```

结果：

| round | events |
|-------|--------|
| A轮 | 56 |
| B轮 | 42 |
| C轮 | 32 |
| D轮 | 21 |
| 天使轮 | 12 |
| Pre-A轮 | 10 |
| 战略投资 | 8 |
| B+轮 | 3 |
| A+轮 | 2 |
| E轮 | 1 |

### 4.4 查询共同投资关系

```cypher
MATCH (i1:AngelInvestor)-[:INVESTS]->(c:StartupCompany)<-[:INVESTS]-(i2:AngelInvestor)
WHERE i1.name = '红杉资本中国' AND i1 <> i2
RETURN i2.name AS co_investor, count(c) AS common_investments
ORDER BY common_investments DESC
LIMIT 5
```

结果：

| co_investor | common_investments |
|-------------|-------------------|
| IDG资本 | 32 |
| 经纬中国 | 28 |
| 腾讯投资 | 24 |
| 高瓴资本 | 18 |
| 真格基金 | 15 |

### 4.5 查询投资路径

```cypher
MATCH path = (i1:AngelInvestor)-[:INVESTS]->(c1:StartupCompany)<-[:INVESTS]-(i2:AngelInvestor)-[:INVESTS]->(c2:StartupCompany)
WHERE i1.name = '红杉资本中国' AND c2.name = '美团' AND i1 <> i2 AND c1 <> c2
RETURN i2.name AS intermediate_investor, c1.name AS intermediate_company
LIMIT 5
```

结果：

| intermediate_investor | intermediate_company |
|-----------------------|----------------------|
| IDG资本 | 字节跳动 |
| 经纬中国 | 滴滴出行 |
| 腾讯投资 | 京东 |
| 高瓴资本 | 拼多多 |
| 真格基金 | 知乎 |

## 5. 结论

通过Neo4j批量导入工具，我们成功地将中国天使投资数据导入到Neo4j图数据库中。导入过程高效、快速，只用了几秒钟就完成了所有数据的导入。

导入后的数据库包含了丰富的节点和关系，可以支持各种复杂的查询和分析。通过Cypher查询语言，我们可以轻松地探索投资机构、被投资公司、投资轮次之间的关系，发现投资模式和趋势。

这个知识图谱为中国天使投资领域的研究和分析提供了强大的工具，可以用于投资决策支持、市场趋势分析、投资机构评估等多种场景。
