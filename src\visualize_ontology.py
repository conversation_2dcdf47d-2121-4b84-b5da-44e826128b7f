#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import subprocess
from graphviz import Source

def visualize_dot_file(dot_file_path, output_format='png'):
    """
    使用Graphviz可视化DOT文件
    
    Args:
        dot_file_path: DOT文件路径
        output_format: 输出格式，如'png', 'svg', 'pdf'等
        
    Returns:
        输出文件路径
    """
    try:
        # 检查是否安装了Graphviz
        subprocess.run(['dot', '-V'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True)
        
        # 读取DOT文件
        with open(dot_file_path, 'r', encoding='utf-8') as f:
            dot_content = f.read()
        
        # 创建输出目录
        output_dir = os.path.dirname(dot_file_path)
        output_file_base = os.path.splitext(os.path.basename(dot_file_path))[0]
        output_file_path = os.path.join(output_dir, f"{output_file_base}.{output_format}")
        
        # 使用Graphviz渲染DOT文件
        src = Source(dot_content)
        src.format = output_format
        src.render(os.path.join(output_dir, output_file_base), view=False, cleanup=True)
        
        print(f"已生成可视化文件: {output_file_path}")
        return output_file_path
    
    except subprocess.CalledProcessError:
        print("错误: 未安装Graphviz或无法运行dot命令")
        print("请安装Graphviz: https://graphviz.org/download/")
        return None
    except Exception as e:
        print(f"可视化DOT文件时出错: {e}")
        return None

def create_ontology_visualization():
    """
    创建本体图的可视化
    """
    dot_file_path = 'models/angel_investment_ontology.dot'
    
    # 检查DOT文件是否存在
    if not os.path.exists(dot_file_path):
        print(f"错误: DOT文件不存在: {dot_file_path}")
        return
    
    # 可视化DOT文件
    output_file_path = visualize_dot_file(dot_file_path, output_format='png')
    
    if output_file_path:
        print("\n本体图可视化完成！")
        print(f"可视化文件保存在: {output_file_path}")
    else:
        print("\n本体图可视化失败！")
        print("请确保已安装Graphviz并且可以运行dot命令")

def create_ontology_html():
    """
    创建本体图的HTML展示页面
    """
    # 读取本体JSON文件
    json_file_path = 'models/angel_investment_ontology.json'
    
    if not os.path.exists(json_file_path):
        print(f"错误: JSON文件不存在: {json_file_path}")
        return
    
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            ontology_data = json.load(f)
        
        # 提取类和属性
        classes = []
        properties = []
        
        for item in ontology_data.get('@graph', []):
            if item.get('@type') == 'owl:Class':
                classes.append({
                    'id': item.get('@id', ''),
                    'label': item.get('rdfs:label', ''),
                    'comment': item.get('rdfs:comment', ''),
                    'subClassOf': item.get('rdfs:subClassOf', {}).get('@id', '')
                })
            elif item.get('@type') in ['owl:DatatypeProperty', 'owl:ObjectProperty']:
                properties.append({
                    'id': item.get('@id', ''),
                    'label': item.get('rdfs:label', ''),
                    'comment': item.get('rdfs:comment', ''),
                    'type': item.get('@type', ''),
                    'domain': item.get('rdfs:domain', ''),
                    'range': item.get('rdfs:range', '')
                })
        
        # 创建HTML内容
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国天使投资知识图谱本体</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .ontology-image {{
            text-align: center;
            margin: 20px 0;
        }}
        .ontology-image img {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .class-hierarchy {{
            margin: 20px 0;
        }}
        .class-item {{
            margin-left: 20px;
        }}
        .property-type {{
            font-style: italic;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>中国天使投资知识图谱本体</h1>
        
        <div class="ontology-image">
            <h2>本体图</h2>
            <img src="angel_investment_ontology.png" alt="天使投资知识图谱本体图">
        </div>
        
        <h2>类层次结构</h2>
        <div class="class-hierarchy">
            <ul>
"""
        
        # 构建类层次结构
        def build_class_hierarchy(parent_id):
            result = ""
            for cls in classes:
                if cls['subClassOf'] == parent_id:
                    result += f"""
                <li>
                    <strong>{cls['label']}</strong> ({cls['id'].split(':')[-1]}) - {cls['comment']}
                    <div class="class-item">
                        <ul>
                            {build_class_hierarchy(cls['id'])}
                        </ul>
                    </div>
                </li>
"""
            return result
        
        html_content += build_class_hierarchy('owl:Thing')
        
        html_content += """
            </ul>
        </div>
        
        <h2>属性列表</h2>
        <table>
            <tr>
                <th>属性名称</th>
                <th>类型</th>
                <th>描述</th>
                <th>定义域</th>
                <th>值域</th>
            </tr>
"""
        
        # 添加属性列表
        for prop in properties:
            prop_type = "数据属性" if prop['type'] == 'owl:DatatypeProperty' else "对象属性"
            
            # 处理定义域
            domain = prop['domain']
            if isinstance(domain, list):
                domain_str = ", ".join([d.get('@id', '').split(':')[-1] for d in domain])
            else:
                domain_str = domain.get('@id', '').split(':')[-1] if isinstance(domain, dict) else str(domain)
            
            # 处理值域
            range_val = prop['range']
            range_str = range_val.get('@id', '').split(':')[-1] if isinstance(range_val, dict) else str(range_val)
            
            html_content += f"""
            <tr>
                <td><strong>{prop['label']}</strong> ({prop['id'].split(':')[-1]})</td>
                <td class="property-type">{prop_type}</td>
                <td>{prop['comment']}</td>
                <td>{domain_str}</td>
                <td>{range_str}</td>
            </tr>
"""
        
        html_content += """
        </table>
        
        <h2>智能问答系统支持的查询类型</h2>
        <ol>
            <li>
                <strong>投资机构信息查询</strong>
                <ul>
                    <li>"红杉资本是什么时候成立的？"</li>
                    <li>"IDG资本的创始人是谁？"</li>
                    <li>"真格基金主要投资哪些领域？"</li>
                </ul>
            </li>
            <li>
                <strong>投资人信息查询</strong>
                <ul>
                    <li>"张磊管理哪些投资机构？"</li>
                    <li>"徐小平投资过哪些公司？"</li>
                </ul>
            </li>
            <li>
                <strong>投资事件查询</strong>
                <ul>
                    <li>"红杉资本投资了哪些公司？"</li>
                    <li>"字节跳动的A轮投资方是谁？"</li>
                    <li>"2020年有哪些重要的天使投资事件？"</li>
                </ul>
            </li>
            <li>
                <strong>投资领域查询</strong>
                <ul>
                    <li>"哪些投资机构关注医疗健康领域？"</li>
                    <li>"教育领域有哪些知名的天使投资机构？"</li>
                </ul>
            </li>
            <li>
                <strong>地理位置查询</strong>
                <ul>
                    <li>"北京有哪些知名的天使投资机构？"</li>
                    <li>"哪些投资机构主要投资上海地区的创业公司？"</li>
                </ul>
            </li>
            <li>
                <strong>统计分析查询</strong>
                <ul>
                    <li>"2021年天使投资轮次的平均投资金额是多少？"</li>
                    <li>"医疗健康领域的投资事件数量趋势如何？"</li>
                    <li>"哪个城市的天使投资活动最活跃？"</li>
                </ul>
            </li>
            <li>
                <strong>关系推理查询</strong>
                <ul>
                    <li>"徐小平和张磊共同投资过哪些公司？"</li>
                    <li>"红杉资本和IDG资本都关注哪些投资领域？"</li>
                </ul>
            </li>
        </ol>
    </div>
</body>
</html>
"""
        
        # 保存HTML文件
        html_file_path = 'models/angel_investment_ontology.html'
        with open(html_file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"已生成HTML展示页面: {html_file_path}")
        return html_file_path
    
    except Exception as e:
        print(f"创建HTML展示页面时出错: {e}")
        return None

def main():
    print("开始创建中国天使投资知识图谱本体可视化...")
    
    # 创建本体图可视化
    create_ontology_visualization()
    
    # 创建HTML展示页面
    create_ontology_html()
    
    print("\n本体可视化完成！")

if __name__ == "__main__":
    main()
