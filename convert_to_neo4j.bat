@echo off
echo 中国天使投资知识图谱 - 数据转换工具
echo ==============================

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请安装Python 3.6+
    pause
    exit /b 1
)

REM 创建必要的目录
if not exist neo4j_import mkdir neo4j_import

echo.
echo 开始转换数据...
python src\convert_to_neo4j_csv.py

if %errorlevel% neq 0 (
    echo 转换过程中出现错误，请检查日志。
    pause
    exit /b 1
)

echo.
echo 数据转换完成！
echo CSV文件已保存到 neo4j_import 目录。
echo.
echo 请使用以下命令将数据导入到Neo4j：
echo.
echo neo4j-admin import --database=angelinvestment ^
echo     --nodes=AngelInvestor=neo4j_import/angel_investor.csv ^
echo     --nodes=VentureCapital=neo4j_import/venture_capital.csv ^
echo     --nodes=StartupCompany=neo4j_import/company.csv ^
echo     --nodes=InvestmentRound=neo4j_import/investment_round.csv ^
echo     --nodes=InvestmentEvent=neo4j_import/investment_event.csv ^
echo     --relationships=INVESTS=neo4j_import/invests_relationship.csv ^
echo     --relationships=HAS_INVESTMENT_EVENT=neo4j_import/has_event_relationship.csv ^
echo     --relationships=HAS_ROUND=neo4j_import/has_round_relationship.csv ^
echo     --delimiter="," --array-delimiter=";" --id-type=STRING
echo.

pause
