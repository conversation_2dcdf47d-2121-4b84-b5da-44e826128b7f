@echo off
echo 中国天使投资智能问答系统
echo ==============================

REM 设置Neo4j连接参数（请根据实际情况修改）
set NEO4J_URI=bolt://localhost:7687
set NEO4J_USER=neo4j
set NEO4J_PASSWORD=password

echo 选择运行模式:
echo 1. 交互式问答
echo 2. 演示场景1: 投资机构投资组合分析
echo 3. 演示场景2: 创业公司融资路径分析

set /p mode="请输入选项 (1/2/3): "

if "%mode%"=="1" (
    python src/angel_investment_qa_system.py --uri=%NEO4J_URI% --user=%NEO4J_USER% --password=%NEO4J_PASSWORD% --demo=0
) else if "%mode%"=="2" (
    python src/angel_investment_qa_system.py --uri=%NEO4J_URI% --user=%NEO4J_USER% --password=%NEO4J_PASSWORD% --demo=1
) else if "%mode%"=="3" (
    python src/angel_investment_qa_system.py --uri=%NEO4J_URI% --user=%NEO4J_USER% --password=%NEO4J_PASSWORD% --demo=2
) else (
    echo 无效的选项!
)

pause
