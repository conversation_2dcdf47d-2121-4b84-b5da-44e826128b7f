#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.sequence import pad_sequences
from tensorflow.keras.models import Model, load_model
from tensorflow.keras.layers import Input, LSTM, Embedding, Dense, TimeDistributed, Dropout, Bidirectional
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping
from sklearn.model_selection import train_test_split
from seqeval.metrics import precision_score, recall_score, f1_score, classification_report
import matplotlib.pyplot as plt
from tensorflow_addons.layers import CRF
from tensorflow_addons.text.crf import crf_log_likelihood

# 设置随机种子，确保结果可复现
np.random.seed(42)
tf.random.set_seed(42)

class NERModel:
    def __init__(self, max_len=100, embedding_dim=200, lstm_units=128):
        """
        初始化NER模型
        
        Args:
            max_len: 序列最大长度
            embedding_dim: 词嵌入维度
            lstm_units: LSTM单元数量
        """
        self.max_len = max_len
        self.embedding_dim = embedding_dim
        self.lstm_units = lstm_units
        self.model = None
        self.word2idx = {}
        self.tag2idx = {}
        self.idx2tag = {}
        
    def prepare_data(self, sentences, tags):
        """
        准备训练数据
        
        Args:
            sentences: 句子列表，每个句子是词语列表
            tags: 标签列表，每个标签列表对应一个句子
            
        Returns:
            X: 填充后的句子索引
            y: 填充后的标签索引（one-hot编码）
        """
        # 构建词汇表和标签表
        words = set([word for sentence in sentences for word in sentence])
        self.word2idx = {word: i + 1 for i, word in enumerate(words)}  # 0留给padding
        self.word2idx['UNK'] = len(self.word2idx) + 1
        
        all_tags = set([tag for tag_list in tags for tag in tag_list])
        self.tag2idx = {tag: i for i, tag in enumerate(all_tags)}
        self.idx2tag = {i: tag for tag, i in self.tag2idx.items()}
        
        # 将词语和标签转换为索引
        X = [[self.word2idx.get(word, self.word2idx['UNK']) for word in sentence] for sentence in sentences]
        y = [[self.tag2idx[tag] for tag in tag_list] for tag_list in tags]
        
        # 填充序列
        X = pad_sequences(X, maxlen=self.max_len, padding='post', value=0)
        y = pad_sequences(y, maxlen=self.max_len, padding='post', value=0)
        
        # 将标签转换为one-hot编码
        num_tags = len(self.tag2idx)
        y_one_hot = np.zeros((len(y), self.max_len, num_tags))
        for i, seq in enumerate(y):
            for j, tag_idx in enumerate(seq):
                if j < self.max_len:
                    y_one_hot[i, j, tag_idx] = 1
        
        return X, y_one_hot
    
    def build_model(self):
        """
        构建BiLSTM-CRF模型
        """
        # 输入层
        input_layer = Input(shape=(self.max_len,))
        
        # 嵌入层
        embedding_layer = Embedding(
            input_dim=len(self.word2idx) + 1,
            output_dim=self.embedding_dim,
            input_length=self.max_len,
            mask_zero=True
        )(input_layer)
        
        # 双向LSTM层
        bilstm_layer = Bidirectional(
            LSTM(units=self.lstm_units, return_sequences=True, recurrent_dropout=0.1)
        )(embedding_layer)
        
        # Dropout层，防止过拟合
        dropout_layer = Dropout(0.1)(bilstm_layer)
        
        # 全连接层
        dense_layer = TimeDistributed(
            Dense(len(self.tag2idx), activation='relu')
        )(dropout_layer)
        
        # CRF层
        crf_layer = CRF(len(self.tag2idx))(dense_layer)
        
        # 构建模型
        self.model = Model(input_layer, crf_layer)
        
        # 编译模型
        self.model.compile(
            optimizer='adam',
            loss=self.crf_loss,
            metrics=[self.crf_accuracy]
        )
        
        return self.model
    
    def crf_loss(self, y_true, y_pred):
        """
        CRF损失函数
        """
        crf_layer = self.model.layers[-1]
        mask = tf.math.logical_not(tf.math.equal(tf.reduce_sum(y_true, axis=-1), 0))
        log_likelihood, _ = crf_log_likelihood(
            y_pred, tf.argmax(y_true, axis=-1), tf.reduce_sum(tf.cast(mask, tf.int64), axis=-1), crf_layer.get_weights()[0]
        )
        return -tf.reduce_mean(log_likelihood)
    
    def crf_accuracy(self, y_true, y_pred):
        """
        CRF准确率
        """
        crf_layer = self.model.layers[-1]
        mask = tf.math.logical_not(tf.math.equal(tf.reduce_sum(y_true, axis=-1), 0))
        y_pred = crf_layer(y_pred, mask=mask)
        y_true = tf.argmax(y_true, axis=-1)
        y_pred = tf.cast(y_pred, tf.int32)
        accuracy = tf.reduce_mean(tf.cast(tf.equal(y_true, y_pred), tf.float32))
        return accuracy
    
    def train(self, X_train, y_train, X_val, y_val, epochs=10, batch_size=32):
        """
        训练模型
        
        Args:
            X_train: 训练集特征
            y_train: 训练集标签
            X_val: 验证集特征
            y_val: 验证集标签
            epochs: 训练轮数
            batch_size: 批次大小
            
        Returns:
            训练历史
        """
        # 创建检查点回调，保存最佳模型
        checkpoint = ModelCheckpoint(
            'best_model.h5',
            monitor='val_crf_accuracy',
            verbose=1,
            save_best_only=True,
            mode='max'
        )
        
        # 早停回调，防止过拟合
        early_stopping = EarlyStopping(
            monitor='val_crf_accuracy',
            patience=3,
            verbose=1,
            mode='max'
        )
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=[checkpoint, early_stopping]
        )
        
        return history
    
    def predict(self, sentences):
        """
        使用模型进行预测
        
        Args:
            sentences: 句子列表，每个句子是词语列表
            
        Returns:
            预测的标签列表
        """
        # 将句子转换为索引
        X = [[self.word2idx.get(word, self.word2idx['UNK']) for word in sentence] for sentence in sentences]
        
        # 记录原始长度
        original_lengths = [len(sentence) for sentence in sentences]
        
        # 填充序列
        X = pad_sequences(X, maxlen=self.max_len, padding='post', value=0)
        
        # 预测
        y_pred = self.model.predict(X)
        
        # 将预测结果转换为标签
        pred_tags = []
        for i, pred in enumerate(y_pred):
            tags = []
            for j in range(original_lengths[i]):
                tags.append(self.idx2tag[np.argmax(pred[j])])
            pred_tags.append(tags)
        
        return pred_tags
    
    def evaluate(self, X_test, y_test):
        """
        评估模型性能
        
        Args:
            X_test: 测试集特征
            y_test: 测试集标签
            
        Returns:
            评估指标
        """
        # 预测
        y_pred = self.model.predict(X_test)
        
        # 将one-hot编码转换为标签索引
        y_test_idx = np.argmax(y_test, axis=-1)
        
        # 将预测结果和真实标签转换为标签
        y_pred_tags = []
        y_test_tags = []
        
        for i in range(len(y_test)):
            pred_tags = []
            test_tags = []
            for j in range(self.max_len):
                if y_test[i, j].sum() != 0:  # 非填充位置
                    pred_tags.append(self.idx2tag[np.argmax(y_pred[i, j])])
                    test_tags.append(self.idx2tag[y_test_idx[i, j]])
            y_pred_tags.append(pred_tags)
            y_test_tags.append(test_tags)
        
        # 计算评估指标
        precision = precision_score(y_test_tags, y_pred_tags)
        recall = recall_score(y_test_tags, y_pred_tags)
        f1 = f1_score(y_test_tags, y_pred_tags)
        report = classification_report(y_test_tags, y_pred_tags)
        
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'report': report
        }
    
    def save(self, model_path, vocab_path):
        """
        保存模型和词汇表
        
        Args:
            model_path: 模型保存路径
            vocab_path: 词汇表保存路径
        """
        self.model.save(model_path)
        
        vocab = {
            'word2idx': self.word2idx,
            'tag2idx': self.tag2idx,
            'idx2tag': self.idx2tag
        }
        
        with open(vocab_path, 'w', encoding='utf-8') as f:
            json.dump(vocab, f, ensure_ascii=False, indent=2)
    
    def load(self, model_path, vocab_path):
        """
        加载模型和词汇表
        
        Args:
            model_path: 模型路径
            vocab_path: 词汇表路径
        """
        self.model = load_model(
            model_path,
            custom_objects={
                'crf_loss': self.crf_loss,
                'crf_accuracy': self.crf_accuracy
            }
        )
        
        with open(vocab_path, 'r', encoding='utf-8') as f:
            vocab = json.load(f)
        
        self.word2idx = vocab['word2idx']
        self.tag2idx = vocab['tag2idx']
        self.idx2tag = vocab['idx2tag']
