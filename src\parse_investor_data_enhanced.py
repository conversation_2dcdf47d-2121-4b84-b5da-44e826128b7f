#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版投资者数据解析脚本
创建更多节点类型和关系，将属性信息转换为独立节点
"""

import json
import csv
import os
import hashlib
import re
from datetime import datetime

def clean_string(s):
    """清理字符串，移除特殊字符"""
    if not s:
        return ""
    # 移除换行符、制表符等
    s = str(s).replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    # 移除多余的空格
    s = ' '.join(s.split())
    return s.strip()

def generate_id(prefix, name):
    """生成唯一ID"""
    if not name:
        name = "unknown"
    # 使用MD5生成短哈希
    hash_obj = hashlib.md5(name.encode('utf-8'))
    return f"{prefix}_{hash_obj.hexdigest()[:8]}"

def extract_founding_date(info):
    """提取成立日期"""
    date_fields = ['成立日期', '成立时间', '注册日期', '创立时间']
    for field in date_fields:
        if field in info and info[field]:
            return clean_string(info[field])
    return ""

def parse_date(date_str):
    """解析日期字符串，返回年、月、日"""
    if not date_str:
        return "", "", ""
    
    # 尝试匹配各种日期格式
    patterns = [
        r'(\d{4})年(\d{1,2})月(\d{1,2})日',
        r'(\d{4})-(\d{1,2})-(\d{1,2})',
        r'(\d{4})/(\d{1,2})/(\d{1,2})',
        r'(\d{4})\.(\d{1,2})\.(\d{1,2})',
        r'(\d{4})年(\d{1,2})月',
        r'(\d{4})-(\d{1,2})',
        r'(\d{4})年',
        r'(\d{4})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, date_str)
        if match:
            groups = match.groups()
            year = groups[0] if len(groups) > 0 else ""
            month = groups[1] if len(groups) > 1 else ""
            day = groups[2] if len(groups) > 2 else ""
            return year, month, day
    
    return "", "", ""

def is_angel_investor(name, info):
    """判断是否为天使投资机构"""
    angel_keywords = ['天使', '种子', '早期', 'Angel', 'Seed', 'Early']
    
    # 检查名称
    for keyword in angel_keywords:
        if keyword in name:
            return True
    
    # 检查描述信息
    description = info.get('description', '')
    for keyword in angel_keywords:
        if keyword in description:
            return True
    
    return False

def extract_description(info):
    """提取描述信息"""
    desc_parts = []
    
    # 按优先级添加信息
    if '公司类型' in info and info['公司类型']:
        desc_parts.append(f"公司类型: {info['公司类型']}")
    
    if '所属行业' in info and info['所属行业']:
        desc_parts.append(f"所属行业: {info['所属行业']}")
    
    if '经营范围' in info and info['经营范围']:
        scope = info['经营范围']
        if len(scope) > 100:
            scope = scope[:100] + "..."
        desc_parts.append(f"经营范围: {scope}")
    
    if '法定代表人' in info and info['法定代表人']:
        desc_parts.append(f"法定代表人: {info['法定代表人']}")
    
    if '外文名' in info and info['外文名']:
        desc_parts.append(f"外文名: {info['外文名']}")
    
    if '公司口号' in info and info['公司口号']:
        desc_parts.append(f"公司口号: {info['公司口号']}")
    
    return "; ".join(desc_parts)

def create_enhanced_csv_files(data_file, output_dir='neo4j_import_enhanced'):
    """创建增强版CSV文件，包含更多节点类型和关系"""
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 读取数据
    with open(data_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if not isinstance(data, list):
        print(f"数据结构不符合预期，应为列表，实际为: {type(data)}")
        return
    
    print(f"共加载 {len(data)} 条投资者数据")
    
    # 定义所有CSV文件
    files = {
        # 节点文件
        'investors': os.path.join(output_dir, 'investors.csv'),
        'companies': os.path.join(output_dir, 'companies.csv'),
        'persons': os.path.join(output_dir, 'persons.csv'),
        'industries': os.path.join(output_dir, 'industries.csv'),
        'locations': os.path.join(output_dir, 'locations.csv'),
        'company_types': os.path.join(output_dir, 'company_types.csv'),
        'founding_dates': os.path.join(output_dir, 'founding_dates.csv'),
        'investment_rounds': os.path.join(output_dir, 'investment_rounds.csv'),
        'investment_events': os.path.join(output_dir, 'investment_events.csv'),
        
        # 关系文件
        'invests': os.path.join(output_dir, 'invests_relationships.csv'),
        'founded_by': os.path.join(output_dir, 'founded_by_relationships.csv'),
        'located_in': os.path.join(output_dir, 'located_in_relationships.csv'),
        'belongs_to_industry': os.path.join(output_dir, 'belongs_to_industry_relationships.csv'),
        'has_type': os.path.join(output_dir, 'has_type_relationships.csv'),
        'founded_on': os.path.join(output_dir, 'founded_on_relationships.csv'),
        'legal_representative': os.path.join(output_dir, 'legal_representative_relationships.csv'),
        'has_investment_event': os.path.join(output_dir, 'has_investment_event_relationships.csv'),
        'has_round': os.path.join(output_dir, 'has_round_relationships.csv')
    }
    
    # 定义字段
    fields = {
        'investors': ['id:ID', 'name', 'description', ':LABEL'],
        'companies': ['id:ID', 'name', 'description', ':LABEL'],
        'persons': ['id:ID', 'name', 'role', 'description', ':LABEL'],
        'industries': ['id:ID', 'name', 'description', ':LABEL'],
        'locations': ['id:ID', 'name', 'type', 'description', ':LABEL'],
        'company_types': ['id:ID', 'name', 'description', ':LABEL'],
        'founding_dates': ['id:ID', 'year', 'month', 'day', 'fullDate', ':LABEL'],
        'investment_rounds': ['id:ID', 'name', 'roundType', 'stage', 'description', ':LABEL'],
        'investment_events': ['id:ID', 'name', 'investmentAmount', 'investmentDate', 'description', ':LABEL'],
        
        'invests': [':START_ID', ':END_ID', 'investmentAmount', 'investmentDate', 'roundType', ':TYPE'],
        'founded_by': [':START_ID', ':END_ID', 'role', ':TYPE'],
        'located_in': [':START_ID', ':END_ID', ':TYPE'],
        'belongs_to_industry': [':START_ID', ':END_ID', ':TYPE'],
        'has_type': [':START_ID', ':END_ID', ':TYPE'],
        'founded_on': [':START_ID', ':END_ID', ':TYPE'],
        'legal_representative': [':START_ID', ':END_ID', ':TYPE'],
        'has_investment_event': [':START_ID', ':END_ID', ':TYPE'],
        'has_round': [':START_ID', ':END_ID', ':TYPE']
    }
    
    # 用于去重的集合
    processed_items = {
        'persons': set(),
        'industries': set(),
        'locations': set(),
        'company_types': set(),
        'founding_dates': set(),
        'companies': set()
    }
    
    # 打开所有文件
    file_handles = {}
    writers = {}
    
    try:
        for key, filepath in files.items():
            file_handles[key] = open(filepath, 'w', newline='', encoding='utf-8-sig')
            writers[key] = csv.DictWriter(file_handles[key], fieldnames=fields[key])
            writers[key].writeheader()
        
        # 预定义投资轮次
        predefined_rounds = [
            {'name': '天使轮', 'roundType': '早期投资', 'stage': '种子期', 'label': 'AngelRound'},
            {'name': '种子轮', 'roundType': '早期投资', 'stage': '种子期', 'label': 'SeedRound'},
            {'name': 'Pre-A轮', 'roundType': '早期投资', 'stage': '成长期', 'label': 'PreARound'},
            {'name': 'A轮', 'roundType': '早期投资', 'stage': '成长期', 'label': 'RoundA'},
            {'name': 'A+轮', 'roundType': '早期投资', 'stage': '成长期', 'label': 'RoundAPlus'},
            {'name': 'B轮', 'roundType': '成长期投资', 'stage': '扩张期', 'label': 'RoundB'},
            {'name': 'B+轮', 'roundType': '成长期投资', 'stage': '扩张期', 'label': 'RoundBPlus'},
            {'name': 'C轮', 'roundType': '成长期投资', 'stage': '扩张期', 'label': 'RoundC'},
            {'name': 'D轮', 'roundType': '后期投资', 'stage': '成熟期', 'label': 'RoundD'},
            {'name': 'E轮', 'roundType': '后期投资', 'stage': '成熟期', 'label': 'RoundE'},
            {'name': 'F轮', 'roundType': '后期投资', 'stage': '成熟期', 'label': 'RoundF'},
            {'name': 'Pre-IPO', 'roundType': '上市投资', 'stage': '上市期', 'label': 'PreIPO'},
            {'name': '新三板定增', 'roundType': '其他投资', 'stage': '未知阶段', 'label': 'NEEQ'},
            {'name': '战略投资', 'roundType': '其他投资', 'stage': '未知阶段', 'label': 'StrategicInvestment'},
            {'name': '并购', 'roundType': '其他投资', 'stage': '未知阶段', 'label': 'Acquisition'},
            {'name': '未知轮次', 'roundType': '其他投资', 'stage': '未知阶段', 'label': 'UnknownRound'}
        ]
        
        # 写入预定义轮次
        for round_info in predefined_rounds:
            round_id = generate_id('round', round_info['name'])
            writers['investment_rounds'].writerow({
                'id:ID': round_id,
                'name': round_info['name'],
                'roundType': round_info['roundType'],
                'stage': round_info['stage'],
                'description': f"{round_info['name']}融资",
                ':LABEL': round_info['label']
            })
        
        # 处理每个投资者
        event_id = 1
        for item in data:
            if 'company_name' not in item or 'info' not in item:
                continue
            
            investor_name = item['company_name']
            info = item['info']
            
            # 生成投资者ID
            investor_id = generate_id('investor', investor_name)
            
            # 判断是否为天使投资机构
            is_angel = is_angel_investor(investor_name, info)
            
            # 写入投资者数据
            writers['investors'].writerow({
                'id:ID': investor_id,
                'name': clean_string(investor_name),
                'description': clean_string(extract_description(info)),
                ':LABEL': 'AngelInvestor' if is_angel else 'VentureCapital'
            })
            
            # 处理创始人
            founder = clean_string(info.get('创始人', ''))
            if founder and founder not in processed_items['persons']:
                processed_items['persons'].add(founder)
                founder_id = generate_id('person', founder)
                writers['persons'].writerow({
                    'id:ID': founder_id,
                    'name': founder,
                    'role': '创始人',
                    'description': f"{investor_name}的创始人",
                    ':LABEL': 'Person'
                })
                
                # 创建创始关系
                writers['founded_by'].writerow({
                    ':START_ID': investor_id,
                    ':END_ID': founder_id,
                    'role': '创始人',
                    ':TYPE': 'FOUNDED_BY'
                })
            
            # 处理法定代表人
            legal_rep = clean_string(info.get('法定代表人', ''))
            if legal_rep and legal_rep not in processed_items['persons']:
                processed_items['persons'].add(legal_rep)
                legal_rep_id = generate_id('person', legal_rep)
                writers['persons'].writerow({
                    'id:ID': legal_rep_id,
                    'name': legal_rep,
                    'role': '法定代表人',
                    'description': f"{investor_name}的法定代表人",
                    ':LABEL': 'Person'
                })
                
                # 创建法定代表人关系
                writers['legal_representative'].writerow({
                    ':START_ID': investor_id,
                    ':END_ID': legal_rep_id,
                    ':TYPE': 'LEGAL_REPRESENTATIVE'
                })
            
            # 处理行业
            industry = clean_string(info.get('所属行业', ''))
            if industry and industry not in processed_items['industries']:
                processed_items['industries'].add(industry)
                industry_id = generate_id('industry', industry)
                writers['industries'].writerow({
                    'id:ID': industry_id,
                    'name': industry,
                    'description': f"{industry}行业",
                    ':LABEL': 'Industry'
                })
                
                # 创建行业关系
                writers['belongs_to_industry'].writerow({
                    ':START_ID': investor_id,
                    ':END_ID': industry_id,
                    ':TYPE': 'BELONGS_TO_INDUSTRY'
                })
            
            # 处理地点
            location = clean_string(info.get('总部地点', ''))
            if location and location not in processed_items['locations']:
                processed_items['locations'].add(location)
                location_id = generate_id('location', location)
                writers['locations'].writerow({
                    'id:ID': location_id,
                    'name': location,
                    'type': '总部地点',
                    'description': f"位于{location}",
                    ':LABEL': 'Location'
                })
                
                # 创建地点关系
                writers['located_in'].writerow({
                    ':START_ID': investor_id,
                    ':END_ID': location_id,
                    ':TYPE': 'LOCATED_IN'
                })
            
            # 处理公司类型
            company_type = clean_string(info.get('公司类型', ''))
            if company_type and company_type not in processed_items['company_types']:
                processed_items['company_types'].add(company_type)
                type_id = generate_id('type', company_type)
                writers['company_types'].writerow({
                    'id:ID': type_id,
                    'name': company_type,
                    'description': f"{company_type}类型的企业",
                    ':LABEL': 'CompanyType'
                })
                
                # 创建类型关系
                writers['has_type'].writerow({
                    ':START_ID': investor_id,
                    ':END_ID': type_id,
                    ':TYPE': 'HAS_TYPE'
                })
            
            # 处理成立日期
            founding_date_str = extract_founding_date(info)
            if founding_date_str:
                year, month, day = parse_date(founding_date_str)
                date_key = f"{year}-{month}-{day}"
                
                if date_key not in processed_items['founding_dates']:
                    processed_items['founding_dates'].add(date_key)
                    date_id = generate_id('date', date_key)
                    writers['founding_dates'].writerow({
                        'id:ID': date_id,
                        'year': year,
                        'month': month,
                        'day': day,
                        'fullDate': founding_date_str,
                        ':LABEL': 'FoundingDate'
                    })
                    
                    # 创建成立日期关系
                    writers['founded_on'].writerow({
                        ':START_ID': investor_id,
                        ':END_ID': date_id,
                        ':TYPE': 'FOUNDED_ON'
                    })
            
            # 创建虚拟被投资公司和投资事件
            investment_round = "天使轮" if is_angel else "A轮"
            
            # 生成被投资公司
            company_name = f"{investor_name}投资项目{event_id}"
            company_id = generate_id('company', company_name)
            
            if company_id not in processed_items['companies']:
                processed_items['companies'].add(company_id)
                writers['companies'].writerow({
                    'id:ID': company_id,
                    'name': company_name,
                    'description': f"获得{investor_name}投资的创业公司",
                    ':LABEL': 'StartupCompany'
                })
            
            # 创建投资事件
            event_name = f"{investor_name}投资{company_name}"
            event_unique_id = f"event_{event_id}"
            
            writers['investment_events'].writerow({
                'id:ID': event_unique_id,
                'name': event_name,
                'investmentAmount': '',
                'investmentDate': founding_date_str,
                'description': f"{investor_name}对{company_name}的{investment_round}投资",
                ':LABEL': 'InvestmentEvent'
            })
            
            # 创建投资关系
            writers['invests'].writerow({
                ':START_ID': investor_id,
                ':END_ID': company_id,
                'investmentAmount': '',
                'investmentDate': founding_date_str,
                'roundType': investment_round,
                ':TYPE': 'INVESTS'
            })
            
            # 创建投资事件关系
            writers['has_investment_event'].writerow({
                ':START_ID': investor_id,
                ':END_ID': event_unique_id,
                ':TYPE': 'HAS_INVESTMENT_EVENT'
            })
            
            writers['has_investment_event'].writerow({
                ':START_ID': company_id,
                ':END_ID': event_unique_id,
                ':TYPE': 'HAS_INVESTMENT_EVENT'
            })
            
            # 创建轮次关系
            round_id = generate_id('round', investment_round)
            writers['has_round'].writerow({
                ':START_ID': event_unique_id,
                ':END_ID': round_id,
                ':TYPE': 'HAS_ROUND'
            })
            
            event_id += 1
        
        print(f"\n已生成增强版CSV文件到目录: {output_dir}")
        print("包含以下文件:")
        for key, filepath in files.items():
            print(f"- {os.path.basename(filepath)}")
        
    finally:
        # 关闭所有文件
        for handle in file_handles.values():
            handle.close()

if __name__ == "__main__":
    data_file = "models/angel_investors_processed.json"
    create_enhanced_csv_files(data_file)
