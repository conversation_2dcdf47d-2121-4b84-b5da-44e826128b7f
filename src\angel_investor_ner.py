#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import numpy as np
from src.bilstm_crf_ner import NERModel
from seqeval.metrics import precision_score, recall_score, f1_score, classification_report

def create_angel_investor_samples():
    """
    创建天使投资公司网页文本样本

    Returns:
        samples: 样本列表，每个样本是一个字典，包含原始文本和标注
    """
    # 创建样本目录
    os.makedirs('data/angel_investor', exist_ok=True)

    # 样本1：红杉资本中国
    sample1 = {
        'name': '红杉资本中国',
        'text': """红杉资本中国基金是全球知名风险投资机构红杉资本在中国设立的基金，成立于2005年，总部位于北京。
红杉资本中国由沈南鹏、张帆等人创立，专注于互联网、消费、医疗健康、企业服务等领域的投资。
红杉资本中国管理着超过200亿美元的资产，投资了包括阿里巴巴、京东、美团、字节跳动等知名企业。
红杉资本中国的投资阶段覆盖了从种子轮到Pre-IPO的各个阶段，是中国最活跃的风险投资机构之一。""",
        'tokens': [],
        'tags': []
    }

    # 样本2：IDG资本
    sample2 = {
        'name': 'IDG资本',
        'text': """IDG资本是全球领先的投资机构，成立于1992年，是最早进入中国市场的外资风险投资机构之一。
IDG资本由熊晓鸽和Patrick J. McGovern共同创立，总部位于北京，在上海、广州、深圳、香港等地设有办公室。
IDG资本专注于TMT、消费品、医疗健康、先进制造等领域的投资，管理资产规模超过200亿美元。
IDG资本投资了百度、腾讯、小米、美团等知名企业，是中国风险投资行业的开拓者和领导者。""",
        'tokens': [],
        'tags': []
    }

    # 手动标注样本1
    sample1['tokens'] = [
        '红', '杉', '资', '本', '中', '国', '基', '金', '是', '全', '球', '知', '名', '风', '险', '投', '资', '机', '构',
        '红', '杉', '资', '本', '在', '中', '国', '设', '立', '的', '基', '金', '，', '成', '立', '于', '2', '0', '0', '5',
        '年', '，', '总', '部', '位', '于', '北', '京', '。', '红', '杉', '资', '本', '中', '国', '由', '沈', '南', '鹏',
        '、', '张', '帆', '等', '人', '创', '立', '，', '专', '注', '于', '互', '联', '网', '、', '消', '费', '、', '医',
        '疗', '健', '康', '、', '企', '业', '服', '务', '等', '领', '域', '的', '投', '资', '。', '红', '杉', '资', '本',
        '中', '国', '管', '理', '着', '超', '过', '2', '0', '0', '亿', '美', '元', '的', '资', '产', '，', '投', '资',
        '了', '包', '括', '阿', '里', '巴', '巴', '、', '京', '东', '、', '美', '团', '、', '字', '节', '跳', '动', '等',
        '知', '名', '企', '业', '。', '红', '杉', '资', '本', '中', '国', '的', '投', '资', '阶', '段', '覆', '盖', '了',
        '从', '种', '子', '轮', '到', 'P', 'r', 'e', '-', 'I', 'P', 'O', '的', '各', '个', '阶', '段', '，', '是', '中',
        '国', '最', '活', '跃', '的', '风', '险', '投', '资', '机', '构', '之', '一', '。'
    ]

    sample1['tags'] = [
        'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'B-LOC', 'I-LOC', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'B-TIME', 'I-TIME', 'I-TIME', 'I-TIME',
        'I-TIME', 'O', 'O', 'O', 'O', 'O', 'B-LOC', 'I-LOC', 'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'B-PER', 'I-PER', 'I-PER',
        'O', 'B-PER', 'I-PER', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG',
        'I-ORG', 'I-ORG', 'O', 'O', 'O', 'O', 'O', 'B-TIME', 'I-TIME', 'I-TIME', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'O', 'O', 'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'B-ORG', 'I-ORG', 'O', 'B-ORG', 'I-ORG', 'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O',
        'O', 'O', 'O', 'O', 'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'B-LOC',
        'I-LOC', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O'
    ]

    # 手动标注样本2
    sample2['tokens'] = [
        'I', 'D', 'G', '资', '本', '是', '全', '球', '领', '先', '的', '投', '资', '机', '构', '，', '成', '立', '于',
        '1', '9', '9', '2', '年', '，', '是', '最', '早', '进', '入', '中', '国', '市', '场', '的', '外', '资', '风',
        '险', '投', '资', '机', '构', '之', '一', '。', 'I', 'D', 'G', '资', '本', '由', '熊', '晓', '鸽', '和', 'P',
        'a', 't', 'r', 'i', 'c', 'k', ' ', 'J', '.', ' ', 'M', 'c', 'G', 'o', 'v', 'e', 'r', 'n', '共', '同', '创',
        '立', '，', '总', '部', '位', '于', '北', '京', '，', '在', '上', '海', '、', '广', '州', '、', '深', '圳',
        '、', '香', '港', '等', '地', '设', '有', '办', '公', '室', '。', 'I', 'D', 'G', '资', '本', '专', '注', '于',
        'T', 'M', 'T', '、', '消', '费', '品', '、', '医', '疗', '健', '康', '、', '先', '进', '制', '造', '等', '领',
        '域', '的', '投', '资', '，', '管', '理', '资', '产', '规', '模', '超', '过', '2', '0', '0', '亿', '美', '元',
        '。', 'I', 'D', 'G', '资', '本', '投', '资', '了', '百', '度', '、', '腾', '讯', '、', '小', '米', '、', '美',
        '团', '等', '知', '名', '企', '业', '，', '是', '中', '国', '风', '险', '投', '资', '行', '业', '的', '开',
        '拓', '者', '和', '领', '导', '者', '。'
    ]

    sample2['tags'] = [
        'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'B-TIME', 'I-TIME', 'I-TIME', 'I-TIME', 'I-TIME', 'O', 'O', 'O', 'O', 'O', 'O', 'B-LOC', 'I-LOC', 'O', 'O', 'O', 'O', 'O', 'O',
        'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'B-PER', 'I-PER', 'I-PER', 'O', 'B-PER',
        'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'I-PER', 'O', 'O', 'O',
        'O', 'O', 'O', 'O', 'O', 'O', 'B-LOC', 'I-LOC', 'O', 'O', 'B-LOC', 'I-LOC', 'O', 'B-LOC', 'I-LOC', 'O', 'B-LOC', 'I-LOC',
        'O', 'B-LOC', 'I-LOC', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'O', 'O',
        'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'B-TIME', 'I-TIME', 'I-TIME', 'O', 'O', 'O',
        'O', 'B-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'I-ORG', 'O', 'O', 'O', 'B-ORG', 'I-ORG', 'O', 'B-ORG', 'I-ORG', 'O', 'B-ORG', 'I-ORG', 'O', 'B-ORG',
        'I-ORG', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'B-LOC', 'I-LOC', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'O', 'O', 'O', 'O', 'O', 'O', 'O'
    ]

    # 将样本保存为文件
    samples = [sample1, sample2]

    for i, sample in enumerate(samples):
        # 保存原始文本
        with open(f'data/angel_investor/sample{i+1}_text.txt', 'w', encoding='utf-8') as f:
            f.write(sample['text'])

        # 保存标注数据
        with open(f'data/angel_investor/sample{i+1}_annotation.txt', 'w', encoding='utf-8') as f:
            for token, tag in zip(sample['tokens'], sample['tags']):
                f.write(f"{token} {tag}\n")

    return samples

def evaluate_model_on_samples(model, samples):
    """
    在样本上评估模型

    Args:
        model: NER模型
        samples: 样本列表

    Returns:
        评估结果
    """
    all_true_tags = []
    all_pred_tags = []

    for sample in samples:
        # 获取真实标签
        true_tags = sample['tags']
        all_true_tags.append(true_tags)

        # 预测标签
        tokens = sample['tokens']
        pred_tags = model.predict([tokens])[0]
        all_pred_tags.append(pred_tags)

        # 打印预测结果
        print(f"\n样本: {sample['name']}")
        print("预测结果:")

        entities = []
        current_entity = None

        for i, (token, true_tag, pred_tag) in enumerate(zip(tokens, true_tags, pred_tags)):
            if pred_tag.startswith('B-'):
                if current_entity:
                    entities.append(current_entity)
                current_entity = {
                    'text': token,
                    'type': pred_tag[2:],
                    'start': i,
                    'end': i,
                    'correct': pred_tag == true_tag
                }
            elif pred_tag.startswith('I-') and current_entity and current_entity['type'] == pred_tag[2:]:
                current_entity['text'] += token
                current_entity['end'] = i
                current_entity['correct'] = current_entity['correct'] and (pred_tag == true_tag)
            else:
                if current_entity:
                    entities.append(current_entity)
                    current_entity = None

        if current_entity:
            entities.append(current_entity)

        # 打印识别出的实体
        for entity in entities:
            correct_mark = "✓" if entity['correct'] else "✗"
            print(f"{correct_mark} {entity['type']}: {entity['text']}")

    # 计算评估指标
    precision = precision_score(all_true_tags, all_pred_tags)
    recall = recall_score(all_true_tags, all_pred_tags)
    f1 = f1_score(all_true_tags, all_pred_tags)
    report = classification_report(all_true_tags, all_pred_tags)

    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'report': report
    }

def main():
    # 创建天使投资公司网页文本样本
    print("创建天使投资公司网页文本样本...")
    samples = create_angel_investor_samples()

    # 加载训练好的模型
    print("加载训练好的模型...")
    model = NERModel()
    model.load('models/ner_model.h5', 'models/ner_vocab.json')

    # 在样本上评估模型
    print("在样本上评估模型...")
    metrics = evaluate_model_on_samples(model, samples)

    print("\n评估结果:")
    print(f"Precision: {metrics['precision']:.4f}")
    print(f"Recall: {metrics['recall']:.4f}")
    print(f"F1 Score: {metrics['f1']:.4f}")
    print("\nClassification Report:")
    print(metrics['report'])

    # 保存评估结果
    with open('models/angel_investor_ner_evaluation.json', 'w', encoding='utf-8') as f:
        json.dump({
            'precision': float(metrics['precision']),
            'recall': float(metrics['recall']),
            'f1': float(metrics['f1']),
            'report': metrics['report']
        }, f, ensure_ascii=False, indent=2)

    print("\n评估结果已保存到 models/angel_investor_ner_evaluation.json")

if __name__ == "__main__":
    main()
